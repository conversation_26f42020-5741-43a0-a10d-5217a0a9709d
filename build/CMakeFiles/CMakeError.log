Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: /usr/bin/clang++ 
Build flags: 
Id flags:  

The output was:
1
/usr/bin/ld: cannot find -lstdc++: No such file or directory
clang: error: linker command failed with exit code 1 (use -v to see invocation)


Detecting CXX compiler ABI info failed to compile with the following output:
Change Dir: /home/<USER>/Desktop/drone_sim_wid_FHE/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/gmake -f Makefile cmTC_03a09/fast && /usr/bin/gmake  -f CMakeFiles/cmTC_03a09.dir/build.make CMakeFiles/cmTC_03a09.dir/build
gmake[1]: Entering directory '/home/<USER>/Desktop/drone_sim_wid_FHE/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_03a09.dir/CMakeCXXCompilerABI.cpp.o
/usr/bin/clang++   -v -MD -MT CMakeFiles/cmTC_03a09.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_03a09.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_03a09.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp
Ubuntu clang version 14.0.0-1ubuntu1.1
Target: x86_64-pc-linux-gnu
Thread model: posix
InstalledDir: /usr/bin
Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/11
Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/12
Selected GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/12
Candidate multilib: .;@m64
Selected multilib: .;@m64
Found CUDA installation: /home/<USER>/anaconda3, version 
 (in-process)
 "/usr/lib/llvm-14/bin/clang" -cc1 -triple x86_64-pc-linux-gnu -emit-obj -mrelax-all --mrelax-relocations -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=all -fmath-errno -ffp-contract=on -fno-rounding-math -mconstructor-aliases -funwind-tables=2 -target-cpu x86-64 -tune-cpu generic -mllvm -treat-scalable-fixed-error-as-warning -debugger-tuning=gdb -v -fcoverage-compilation-dir=/home/<USER>/Desktop/drone_sim_wid_FHE/build/CMakeFiles/CMakeTmp -resource-dir /usr/lib/llvm-14/lib/clang/14.0.0 -dependency-file CMakeFiles/cmTC_03a09.dir/CMakeCXXCompilerABI.cpp.o.d -MT CMakeFiles/cmTC_03a09.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -internal-isystem /usr/bin/../lib/gcc/x86_64-linux-gnu/12/../../../../include/c++ -internal-isystem /usr/bin/../lib/gcc/x86_64-linux-gnu/12/../../../../include/c++/x86_64-linux-gnu -internal-isystem /usr/bin/../lib/gcc/x86_64-linux-gnu/12/../../../../include/c++/backward -internal-isystem /usr/lib/llvm-14/lib/clang/14.0.0/include -internal-isystem /usr/local/include -internal-isystem /usr/bin/../lib/gcc/x86_64-linux-gnu/12/../../../../x86_64-linux-gnu/include -internal-externc-isystem /usr/include/x86_64-linux-gnu -internal-externc-isystem /include -internal-externc-isystem /usr/include -fdeprecated-macro -fdebug-compilation-dir=/home/<USER>/Desktop/drone_sim_wid_FHE/build/CMakeFiles/CMakeTmp -ferror-limit 19 -fgnuc-version=4.2.1 -fcxx-exceptions -fexceptions -faddrsig -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_03a09.dir/CMakeCXXCompilerABI.cpp.o -x c++ /usr/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp
clang -cc1 version 14.0.0 based upon LLVM 14.0.0 default target x86_64-pc-linux-gnu
ignoring nonexistent directory "/usr/bin/../lib/gcc/x86_64-linux-gnu/12/../../../../include/c++/x86_64-linux-gnu"
ignoring nonexistent directory "/usr/bin/../lib/gcc/x86_64-linux-gnu/12/../../../../include/c++/backward"
ignoring nonexistent directory "/usr/bin/../lib/gcc/x86_64-linux-gnu/12/../../../../x86_64-linux-gnu/include"
ignoring nonexistent directory "/include"
#include "..." search starts here:
#include <...> search starts here:
 /usr/bin/../lib/gcc/x86_64-linux-gnu/12/../../../../include/c++
 /usr/lib/llvm-14/lib/clang/14.0.0/include
 /usr/local/include
 /usr/include/x86_64-linux-gnu
 /usr/include
End of search list.
Linking CXX executable cmTC_03a09
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_03a09.dir/link.txt --verbose=1
/usr/bin/clang++  -v CMakeFiles/cmTC_03a09.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_03a09 
Ubuntu clang version 14.0.0-1ubuntu1.1
Target: x86_64-pc-linux-gnu
Thread model: posix
InstalledDir: /usr/bin
Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/11
Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/12
Selected GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/12
Candidate multilib: .;@m64
Selected multilib: .;@m64
Found CUDA installation: /home/<USER>/anaconda3, version 
 "/usr/bin/ld" -pie -z relro --hash-style=gnu --build-id --eh-frame-hdr -m elf_x86_64 -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_03a09 /lib/x86_64-linux-gnu/Scrt1.o /lib/x86_64-linux-gnu/crti.o /usr/bin/../lib/gcc/x86_64-linux-gnu/12/crtbeginS.o -L/usr/bin/../lib/gcc/x86_64-linux-gnu/12 -L/usr/bin/../lib/gcc/x86_64-linux-gnu/12/../../../../lib64 -L/lib/x86_64-linux-gnu -L/lib/../lib64 -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib64 -L/usr/lib/llvm-14/bin/../lib -L/lib -L/usr/lib CMakeFiles/cmTC_03a09.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/bin/../lib/gcc/x86_64-linux-gnu/12/crtendS.o /lib/x86_64-linux-gnu/crtn.o
/usr/bin/ld: cannot find -lstdc++: No such file or directory
clang: error: linker command failed with exit code 1 (use -v to see invocation)
gmake[1]: *** [CMakeFiles/cmTC_03a09.dir/build.make:100: cmTC_03a09] Error 1
gmake[1]: Leaving directory '/home/<USER>/Desktop/drone_sim_wid_FHE/build/CMakeFiles/CMakeTmp'
gmake: *** [Makefile:127: cmTC_03a09/fast] Error 2




Determining if the CXX compiler works failed with the following output:
Change Dir: /home/<USER>/Desktop/drone_sim_wid_FHE/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/gmake -f Makefile cmTC_03aa8/fast && /usr/bin/gmake  -f CMakeFiles/cmTC_03aa8.dir/build.make CMakeFiles/cmTC_03aa8.dir/build
gmake[1]: Entering directory '/home/<USER>/Desktop/drone_sim_wid_FHE/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_03aa8.dir/testCXXCompiler.cxx.o
/usr/bin/clang++    -MD -MT CMakeFiles/cmTC_03aa8.dir/testCXXCompiler.cxx.o -MF CMakeFiles/cmTC_03aa8.dir/testCXXCompiler.cxx.o.d -o CMakeFiles/cmTC_03aa8.dir/testCXXCompiler.cxx.o -c /home/<USER>/Desktop/drone_sim_wid_FHE/build/CMakeFiles/CMakeTmp/testCXXCompiler.cxx
Linking CXX executable cmTC_03aa8
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_03aa8.dir/link.txt --verbose=1
/usr/bin/clang++ CMakeFiles/cmTC_03aa8.dir/testCXXCompiler.cxx.o -o cmTC_03aa8 
/usr/bin/ld: cannot find -lstdc++: No such file or directory
clang: error: linker command failed with exit code 1 (use -v to see invocation)
gmake[1]: *** [CMakeFiles/cmTC_03aa8.dir/build.make:100: cmTC_03aa8] Error 1
gmake[1]: Leaving directory '/home/<USER>/Desktop/drone_sim_wid_FHE/build/CMakeFiles/CMakeTmp'
gmake: *** [Makefile:127: cmTC_03aa8/fast] Error 2



Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
Compiler: /usr/bin/clang++ 
Build flags: 
Id flags:  

The output was:
1
/usr/bin/ld: cannot find -lstdc++: No such file or directory
clang: error: linker command failed with exit code 1 (use -v to see invocation)


Detecting CXX compiler ABI info failed to compile with the following output:
Change Dir: /home/<USER>/Desktop/drone_sim_wid_FHE/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/gmake -f Makefile cmTC_c9978/fast && /usr/bin/gmake  -f CMakeFiles/cmTC_c9978.dir/build.make CMakeFiles/cmTC_c9978.dir/build
gmake[1]: Entering directory '/home/<USER>/Desktop/drone_sim_wid_FHE/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_c9978.dir/CMakeCXXCompilerABI.cpp.o
/usr/bin/clang++   -v -MD -MT CMakeFiles/cmTC_c9978.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_c9978.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_c9978.dir/CMakeCXXCompilerABI.cpp.o -c /usr/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp
Ubuntu clang version 14.0.0-1ubuntu1.1
Target: x86_64-pc-linux-gnu
Thread model: posix
InstalledDir: /usr/bin
Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/11
Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/12
Selected GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/12
Candidate multilib: .;@m64
Selected multilib: .;@m64
Found CUDA installation: /home/<USER>/anaconda3, version 
 (in-process)
 "/usr/lib/llvm-14/bin/clang" -cc1 -triple x86_64-pc-linux-gnu -emit-obj -mrelax-all --mrelax-relocations -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=all -fmath-errno -ffp-contract=on -fno-rounding-math -mconstructor-aliases -funwind-tables=2 -target-cpu x86-64 -tune-cpu generic -mllvm -treat-scalable-fixed-error-as-warning -debugger-tuning=gdb -v -fcoverage-compilation-dir=/home/<USER>/Desktop/drone_sim_wid_FHE/build/CMakeFiles/CMakeTmp -resource-dir /usr/lib/llvm-14/lib/clang/14.0.0 -dependency-file CMakeFiles/cmTC_c9978.dir/CMakeCXXCompilerABI.cpp.o.d -MT CMakeFiles/cmTC_c9978.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -internal-isystem /usr/bin/../lib/gcc/x86_64-linux-gnu/12/../../../../include/c++ -internal-isystem /usr/bin/../lib/gcc/x86_64-linux-gnu/12/../../../../include/c++/x86_64-linux-gnu -internal-isystem /usr/bin/../lib/gcc/x86_64-linux-gnu/12/../../../../include/c++/backward -internal-isystem /usr/lib/llvm-14/lib/clang/14.0.0/include -internal-isystem /usr/local/include -internal-isystem /usr/bin/../lib/gcc/x86_64-linux-gnu/12/../../../../x86_64-linux-gnu/include -internal-externc-isystem /usr/include/x86_64-linux-gnu -internal-externc-isystem /include -internal-externc-isystem /usr/include -fdeprecated-macro -fdebug-compilation-dir=/home/<USER>/Desktop/drone_sim_wid_FHE/build/CMakeFiles/CMakeTmp -ferror-limit 19 -fgnuc-version=4.2.1 -fcxx-exceptions -fexceptions -faddrsig -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_c9978.dir/CMakeCXXCompilerABI.cpp.o -x c++ /usr/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp
clang -cc1 version 14.0.0 based upon LLVM 14.0.0 default target x86_64-pc-linux-gnu
ignoring nonexistent directory "/usr/bin/../lib/gcc/x86_64-linux-gnu/12/../../../../include/c++/x86_64-linux-gnu"
ignoring nonexistent directory "/usr/bin/../lib/gcc/x86_64-linux-gnu/12/../../../../include/c++/backward"
ignoring nonexistent directory "/usr/bin/../lib/gcc/x86_64-linux-gnu/12/../../../../x86_64-linux-gnu/include"
ignoring nonexistent directory "/include"
#include "..." search starts here:
#include <...> search starts here:
 /usr/bin/../lib/gcc/x86_64-linux-gnu/12/../../../../include/c++
 /usr/lib/llvm-14/lib/clang/14.0.0/include
 /usr/local/include
 /usr/include/x86_64-linux-gnu
 /usr/include
End of search list.
Linking CXX executable cmTC_c9978
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_c9978.dir/link.txt --verbose=1
/usr/bin/clang++  -v CMakeFiles/cmTC_c9978.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_c9978 
Ubuntu clang version 14.0.0-1ubuntu1.1
Target: x86_64-pc-linux-gnu
Thread model: posix
InstalledDir: /usr/bin
Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/11
Found candidate GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/12
Selected GCC installation: /usr/bin/../lib/gcc/x86_64-linux-gnu/12
Candidate multilib: .;@m64
Selected multilib: .;@m64
Found CUDA installation: /home/<USER>/anaconda3, version 
 "/usr/bin/ld" -pie -z relro --hash-style=gnu --build-id --eh-frame-hdr -m elf_x86_64 -dynamic-linker /lib64/ld-linux-x86-64.so.2 -o cmTC_c9978 /lib/x86_64-linux-gnu/Scrt1.o /lib/x86_64-linux-gnu/crti.o /usr/bin/../lib/gcc/x86_64-linux-gnu/12/crtbeginS.o -L/usr/bin/../lib/gcc/x86_64-linux-gnu/12 -L/usr/bin/../lib/gcc/x86_64-linux-gnu/12/../../../../lib64 -L/lib/x86_64-linux-gnu -L/lib/../lib64 -L/usr/lib/x86_64-linux-gnu -L/usr/lib/../lib64 -L/usr/lib/llvm-14/bin/../lib -L/lib -L/usr/lib CMakeFiles/cmTC_c9978.dir/CMakeCXXCompilerABI.cpp.o -lstdc++ -lm -lgcc_s -lgcc -lc -lgcc_s -lgcc /usr/bin/../lib/gcc/x86_64-linux-gnu/12/crtendS.o /lib/x86_64-linux-gnu/crtn.o
/usr/bin/ld: cannot find -lstdc++: No such file or directory
clang: error: linker command failed with exit code 1 (use -v to see invocation)
gmake[1]: *** [CMakeFiles/cmTC_c9978.dir/build.make:100: cmTC_c9978] Error 1
gmake[1]: Leaving directory '/home/<USER>/Desktop/drone_sim_wid_FHE/build/CMakeFiles/CMakeTmp'
gmake: *** [Makefile:127: cmTC_c9978/fast] Error 2




Determining if the CXX compiler works failed with the following output:
Change Dir: /home/<USER>/Desktop/drone_sim_wid_FHE/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/gmake -f Makefile cmTC_c23ca/fast && /usr/bin/gmake  -f CMakeFiles/cmTC_c23ca.dir/build.make CMakeFiles/cmTC_c23ca.dir/build
gmake[1]: Entering directory '/home/<USER>/Desktop/drone_sim_wid_FHE/build/CMakeFiles/CMakeTmp'
Building CXX object CMakeFiles/cmTC_c23ca.dir/testCXXCompiler.cxx.o
/usr/bin/clang++    -MD -MT CMakeFiles/cmTC_c23ca.dir/testCXXCompiler.cxx.o -MF CMakeFiles/cmTC_c23ca.dir/testCXXCompiler.cxx.o.d -o CMakeFiles/cmTC_c23ca.dir/testCXXCompiler.cxx.o -c /home/<USER>/Desktop/drone_sim_wid_FHE/build/CMakeFiles/CMakeTmp/testCXXCompiler.cxx
Linking CXX executable cmTC_c23ca
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_c23ca.dir/link.txt --verbose=1
/usr/bin/clang++ CMakeFiles/cmTC_c23ca.dir/testCXXCompiler.cxx.o -o cmTC_c23ca 
/usr/bin/ld: cannot find -lstdc++: No such file or directory
clang: error: linker command failed with exit code 1 (use -v to see invocation)
gmake[1]: *** [CMakeFiles/cmTC_c23ca.dir/build.make:100: cmTC_c23ca] Error 1
gmake[1]: Leaving directory '/home/<USER>/Desktop/drone_sim_wid_FHE/build/CMakeFiles/CMakeTmp'
gmake: *** [Makefile:127: cmTC_c23ca/fast] Error 2



