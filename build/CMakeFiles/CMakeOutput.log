The system is: Linux - 6.8.0-65-generic - x86_64
Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: /usr/bin/clang++ 
Build flags: 
Id flags: -c 

The output was:
0


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"

The CXX compiler identification is Clang, found in "/home/<USER>/Desktop/drone_sim_wid_FHE/build/CMakeFiles/3.22.1/CompilerIdCXX/CMakeCXXCompilerId.o"

Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: /usr/bin/clang++ 
Build flags: 
Id flags: -c 

The output was:
0


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"

The CXX compiler identification is Clang, found in "/home/<USER>/Desktop/drone_sim_wid_FHE/build/CMakeFiles/3.22.1/CompilerIdCXX/CMakeCXXCompilerId.o"

