#include <iostream>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <queue>
#include <random>
#include <cmath>
#include <algorithm>
#include <string>
#include <tuple>
#include <functional>
#include <chrono>
#include <thread>

// Custom hash function for pairs to use in unordered sets/maps
struct PairHash {
    template <class T1, class T2>
    std::size_t operator()(const std::pair<T1, T2>& p) const {
        auto h1 = std::hash<T1>{}(p.first);
        auto h2 = std::hash<T2>{}(p.second);
        return h1 ^ (h2 << 1);
    }
};

// 3D Vector class
class Vec3 {
public:
    double x, y, z;

    Vec3() : x(0), y(0), z(0) {}
    Vec3(double x, double y, double z) : x(x), y(y), z(z) {}

    Vec3 operator+(const Vec3& other) const {
        return Vec3(x + other.x, y + other.y, z + other.z);
    }

    Vec3 operator-(const Vec3& other) const {
        return Vec3(x - other.x, y - other.y, z - other.z);
    }

    Vec3 operator*(double scalar) const {
        return Vec3(x * scalar, y * scalar, z * scalar);
    }

    Vec3& operator/=(double scalar) {
        x /= scalar;
        y /= scalar;
        z /= scalar;
        return *this;
    }

    double norm() const {
        return std::sqrt(x*x + y*y + z*z);
    }

    void normalize() {
        double length = norm();
        if (length > 0) {
            *this /= length;
        }
    }
};

// Drone Trajectory class
class DroneTrajectory {
public:
    Vec3 start_point;
    std::string trajectory_type;
    double velocity;
    double radius;
    double angular_velocity;
    double vertical_velocity;
    Vec3 direction;

    DroneTrajectory(const Vec3& start) : start_point(start) {
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_real_distribution<> vel_dist(0.5, 2.0);
        std::uniform_real_distribution<> radius_dist(20.0, 50.0);
        std::uniform_real_distribution<> ang_vel_dist(0.5, 1.5);
        std::uniform_real_distribution<> vert_vel_dist(0.2, 0.8);
        std::uniform_real_distribution<> dir_dist(-1.0, 1.0);
        std::uniform_int_distribution<> type_dist(0, 2);

        int type_idx = type_dist(gen);
        if (type_idx == 0) trajectory_type = "straight";
        else if (type_idx == 1) trajectory_type = "circular";
        else trajectory_type = "helical";

        velocity = vel_dist(gen);
        radius = radius_dist(gen);
        angular_velocity = ang_vel_dist(gen);
        vertical_velocity = (trajectory_type == "helical") ? vert_vel_dist(gen) : 0;

        if (trajectory_type == "straight") {
            direction = Vec3(dir_dist(gen), dir_dist(gen), dir_dist(gen));
            direction.normalize();
        }
    }

    Vec3 get_position(double t) const {
        if (trajectory_type == "straight") {
            return Vec3(
                start_point.x + velocity * t * direction.x,
                start_point.y + velocity * t * direction.y,
                start_point.z + velocity * t * direction.z
            );
        } else if (trajectory_type == "circular") {
            return Vec3(
                start_point.x + radius * std::cos(angular_velocity * t),
                start_point.y + radius * std::sin(angular_velocity * t),
                start_point.z
            );
        } else { // helical
            return Vec3(
                start_point.x + radius * std::cos(angular_velocity * t),
                start_point.y + radius * std::sin(angular_velocity * t),
                start_point.z + vertical_velocity * t
            );
        }
    }
};

// Graph Edge structure
struct Edge {
    std::string from;
    std::string to;
    double weight;

    Edge(const std::string& f, const std::string& t, double w)
        : from(f), to(t), weight(w) {}
};

// Graph class
class Graph {
public:
    std::unordered_map<std::string, Vec3> node_positions;
    std::unordered_map<std::string, std::vector<Edge>> adjacency_list;

    void add_node(const std::string& id, const Vec3& pos) {
        node_positions[id] = pos;
        if (adjacency_list.find(id) == adjacency_list.end()) {
            adjacency_list[id] = std::vector<Edge>();
        }
    }

    void add_edge(const std::string& from, const std::string& to, double weight) {
        adjacency_list[from].push_back(Edge(from, to, weight));
    }

    bool has_edge(const std::string& from, const std::string& to) const {
        if (adjacency_list.find(from) == adjacency_list.end()) return false;
        
        const auto& edges = adjacency_list.at(from);
        return std::any_of(edges.begin(), edges.end(), 
            [&to](const Edge& e) { return e.to == to; });
    }

    double get_edge_weight(const std::string& from, const std::string& to) const {
        if (adjacency_list.find(from) == adjacency_list.end()) return -1;
        
        const auto& edges = adjacency_list.at(from);
        for (const auto& edge : edges) {
            if (edge.to == to) return edge.weight;
        }
        return -1;
    }

    std::vector<std::string> get_nodes() const {
        std::vector<std::string> nodes;
        for (const auto& pair : node_positions) {
            nodes.push_back(pair.first);
        }
        return nodes;
    }

    std::vector<std::string> get_neighbors(const std::string& node) const {
        std::vector<std::string> neighbors;
        if (adjacency_list.find(node) == adjacency_list.end()) return neighbors;
        
        const auto& edges = adjacency_list.at(node);
        for (const auto& edge : edges) {
            neighbors.push_back(edge.to);
        }
        return neighbors;
    }

    std::vector<std::pair<std::string, std::string>> get_edges() const {
        std::vector<std::pair<std::string, std::string>> edges;
        for (const auto& pair : adjacency_list) {
            for (const auto& edge : pair.second) {
                edges.emplace_back(edge.from, edge.to);
            }
        }
        return edges;
    }
};

// Dynamic Drone Path Finding class
class DynamicDronePathFinding {
private:
    int num_drones;
    Vec3 source;
    Vec3 destination;
    std::vector<DroneTrajectory> drone_trajectories;
    
    // Ramalingam and Reps' algorithm specific attributes
    std::unordered_map<std::string, double> distances;  // Shortest path distances
    std::unordered_map<std::string, std::string> parents;  // Parent pointers for shortest paths
    std::unordered_set<std::string> affected_vertices;  // Vertices affected by graph changes
    Graph prev_graph;  // Previous graph for change detection
    bool is_first_run;  // Flag to check if this is the first run

    // Comparison function for priority queue
    struct CompareDistance {
        bool operator()(const std::pair<double, std::string>& a, const std::pair<double, std::string>& b) {
            return a.first > b.first;  // Min priority queue
        }
    };

public:
    DynamicDronePathFinding(int num_drones) 
        : num_drones(num_drones), 
          source(Vec3(-10, -10, 0)), 
          destination(Vec3(24, 30, 10)),
          is_first_run(true) {
        initialize_drone_trajectories();
    }

    void initialize_drone_trajectories() {
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_real_distribution<> x_dist(std::min(source.x, destination.x) - 50, std::max(source.x, destination.x) + 50);
        std::uniform_real_distribution<> y_dist(std::min(source.y, destination.y) - 50, std::max(source.y, destination.y) + 50);
        std::uniform_real_distribution<> z_dist(std::min(source.z, destination.z), std::max(source.z, destination.z) + 50);

        for (int i = 0; i < num_drones; ++i) {
            Vec3 start_point(x_dist(gen), y_dist(gen), z_dist(gen));
            drone_trajectories.push_back(DroneTrajectory(start_point));
        }
    }

    std::vector<Vec3> get_drone_positions(double t) const {
        std::vector<Vec3> positions;
        for (const auto& traj : drone_trajectories) {
            positions.push_back(traj.get_position(t));
        }
        return positions;
    }

    Graph create_distance_graph(const std::vector<Vec3>& drone_positions) {
        Graph G;
        
        // Add source and destination nodes
        G.add_node("source", source);
        G.add_node("destination", destination);
        
        // Add drone nodes
        for (int i = 0; i < num_drones; ++i) {
            G.add_node("drone_" + std::to_string(i), drone_positions[i]);
        }
        
        // Add edges between drones
        for (int i = 0; i < num_drones; ++i) {
            for (int j = i + 1; j < num_drones; ++j) {
                Vec3 diff = drone_positions[i] - drone_positions[j];
                double dist = diff.norm();
                if (dist <= 100) {  // Only connect drones within range
                    G.add_edge("drone_" + std::to_string(i), "drone_" + std::to_string(j), dist);
                    G.add_edge("drone_" + std::to_string(j), "drone_" + std::to_string(i), dist);
                }
            }
        }
        
        // Add edges from source to nearby drones
        for (int i = 0; i < num_drones; ++i) {
            Vec3 diff = source - drone_positions[i];
            double dist = diff.norm();
            if (dist <= 150) {  // Larger range for source/destination
                G.add_edge("source", "drone_" + std::to_string(i), dist);
            }
        }
        
        // Add edges from drones to destination
        for (int i = 0; i < num_drones; ++i) {
            Vec3 diff = drone_positions[i] - destination;
            double dist = diff.norm();
            if (dist <= 150) {  // Larger range for source/destination
                G.add_edge("drone_" + std::to_string(i), "destination", dist);
            }
        }
        
        return G;
    }

    std::tuple<std::unordered_set<std::pair<std::string, std::string>, PairHash>, 
               std::unordered_set<std::pair<std::string, std::string>, PairHash>,
               std::unordered_map<std::pair<std::string, std::string>, double, PairHash>> 
    detect_graph_changes(const Graph& G) {
        std::unordered_set<std::pair<std::string, std::string>, PairHash> deleted_edges;
        std::unordered_set<std::pair<std::string, std::string>, PairHash> added_edges;
        std::unordered_map<std::pair<std::string, std::string>, double, PairHash> modified_weights;
        
        if (is_first_run) {
            // First run, consider all edges as added
            for (const auto& edge_pair : G.get_edges()) {
                added_edges.insert(edge_pair);
            }
            return std::make_tuple(deleted_edges, added_edges, modified_weights);
        }
        
        // Find deleted edges
        for (const auto& edge_pair : prev_graph.get_edges()) {
            if (!G.has_edge(edge_pair.first, edge_pair.second)) {
                deleted_edges.insert(edge_pair);
            }
        }
        
        // Find added edges
        for (const auto& edge_pair : G.get_edges()) {
            if (!prev_graph.has_edge(edge_pair.first, edge_pair.second)) {
                added_edges.insert(edge_pair);
            }
        }
        
        // Find modified edge weights
        for (const auto& edge_pair : G.get_edges()) {
            if (prev_graph.has_edge(edge_pair.first, edge_pair.second)) {
                double old_weight = prev_graph.get_edge_weight(edge_pair.first, edge_pair.second);
                double new_weight = G.get_edge_weight(edge_pair.first, edge_pair.second);
                if (std::abs(old_weight - new_weight) > 1e-6) {  // Account for floating point errors
                    modified_weights[edge_pair] = new_weight;
                }
            }
        }
        
        return std::make_tuple(deleted_edges, added_edges, modified_weights);
    }

    void initialize_single_source(const Graph& G, const std::string& source) {
        distances.clear();
        parents.clear();
        
        for (const auto& node : G.get_nodes()) {
            distances[node] = std::numeric_limits<double>::infinity();
            parents[node] = "";
        }
        distances[source] = 0;
    }

    void dijkstra(const Graph& G, const std::string& source) {
        initialize_single_source(G, source);
        
        std::priority_queue<std::pair<double, std::string>, 
                          std::vector<std::pair<double, std::string>>, 
                          CompareDistance> pq;
        std::unordered_set<std::string> visited;
        
        pq.push({0, source});
        
        while (!pq.empty()) {
            auto [dist, current] = pq.top();
            pq.pop();
            
            if (visited.find(current) != visited.end()) {
                continue;
            }
            
            visited.insert(current);
            
            for (const auto& neighbor : G.get_neighbors(current)) {
                double weight = G.get_edge_weight(current, neighbor);
                if (distances[current] + weight < distances[neighbor]) {
                    distances[neighbor] = distances[current] + weight;
                    parents[neighbor] = current;
                    pq.push({distances[neighbor], neighbor});
                }
            }
        }
    }

    void update_shortest_paths(const Graph& G, const std::string& source) {
        // If this is the first run or major changes detected, recompute from scratch
        if (is_first_run || G.get_nodes().size() != prev_graph.get_nodes().size()) {
            initialize_single_source(G, source);
            dijkstra(G, source);
            prev_graph = G;
            is_first_run = false;
            return;
        }

        // Detect graph changes
        auto [deleted_edges, added_edges, modified_weights] = detect_graph_changes(G);

        // If no changes, return
        if (deleted_edges.empty() && added_edges.empty() && modified_weights.empty()) {
            return;
        }

        // Process deleted edges
        for (const auto& edge : deleted_edges) {
            auto u = edge.first;
            auto v = edge.second;
            if (parents.find(v) != parents.end() && parents[v] == u) {
                // Edge was part of shortest path, need to update
                affected_vertices.insert(v);
            }
        }

        // Process added edges and modified weights
        for (const auto& edge : added_edges) {
            auto u = edge.first;
            auto v = edge.second;
            
            if (distances.find(u) == distances.end() || distances.find(v) == distances.end()) {
                continue;
            }

            double weight = G.get_edge_weight(u, v);
            if (distances[u] + weight < distances[v]) {
                // Found a shorter path
                distances[v] = distances[u] + weight;
                parents[v] = u;
                affected_vertices.insert(v);
            }
        }

        for (const auto& edge_weight_pair : modified_weights) {
            auto u = edge_weight_pair.first.first;
            auto v = edge_weight_pair.first.second;
            double weight = edge_weight_pair.second;
            
            if (distances.find(u) == distances.end() || distances.find(v) == distances.end()) {
                continue;
            }

            if (distances[u] + weight < distances[v]) {
                // Found a shorter path
                distances[v] = distances[u] + weight;
                parents[v] = u;
                affected_vertices.insert(v);
            }
        }

        // Process affected vertices
        while (!affected_vertices.empty()) {
            auto it = affected_vertices.begin();
            std::string v = *it;
            affected_vertices.erase(it);

            // Find the new shortest path to v
            double min_dist = std::numeric_limits<double>::infinity();
            std::string min_parent = "";

            for (const auto& u : G.get_nodes()) {
                if (!G.has_edge(u, v) || distances.find(u) == distances.end()) {
                    continue;
                }

                double weight = G.get_edge_weight(u, v);
                if (distances[u] + weight < min_dist) {
                    min_dist = distances[u] + weight;
                    min_parent = u;
                }
            }

            // Update distance and parent
            if (min_dist < distances[v]) {
                distances[v] = min_dist;
                parents[v] = min_parent;

                // Propagate changes to neighbors
                for (const auto& w : G.get_neighbors(v)) {
                    if (distances.find(w) == distances.end()) {
                        continue;
                    }

                    double weight = G.get_edge_weight(v, w);
                    if (distances[v] + weight < distances[w]) {
                        distances[w] = distances[v] + weight;
                        parents[w] = v;
                        affected_vertices.insert(w);
                    }
                }
            }
        }
    }

    std::pair<std::vector<std::string>, double> find_shortest_path(const std::vector<Vec3>& drone_positions) {
        Graph G = create_distance_graph(drone_positions);
        
        // Update shortest paths based on graph changes
        update_shortest_paths(G, "source");
        
        // Store previous graph for next iteration
        prev_graph = G;
        
        // Reconstruct the path
        if (distances.find("destination") == distances.end() || 
            distances["destination"] == std::numeric_limits<double>::infinity()) {
            return {{}, std::numeric_limits<double>::infinity()};
        }
        
        std::vector<std::string> path = {"destination"};
        std::string current = "destination";
        
        while (current != "source") {
            if (parents.find(current) == parents.end() || parents[current].empty()) {
                return {{}, std::numeric_limits<double>::infinity()};
            }
            current = parents[current];
            path.push_back(current);
        }
        
        std::reverse(path.begin(), path.end());
        return {path, distances["destination"]};
    }

    void simulate(double simulation_time, int time_steps) {
        std::vector<std::string> prev_path;

        for (int i = 0; i < time_steps; ++i) {
            double t = simulation_time * i / (time_steps - 1);
            
            // Get current drone positions
            auto drone_positions = get_drone_positions(t);
            
            // Find current shortest path
            auto [path, path_length] = find_shortest_path(drone_positions);
            
            // Log information
            std::cout << "Time: " << t << ", Path Length: ";
            if (path_length == std::numeric_limits<double>::infinity()) {
                std::cout << "INFINITY (No path found)" << std::endl;
            } else {
                std::cout << path_length << std::endl;
            }
            
            // Log path
            if (!path.empty()) {
                std::cout << "Path: ";
                for (size_t j = 0; j < path.size(); ++j) {
                    std::cout << path[j];
                    if (j < path.size() - 1) std::cout << " -> ";
                }
                std::cout << std::endl;
                
                // Check if path has changed
                if (prev_path != path) {
                    std::cout << "NODE SWITCH DETECTED!" << std::endl;
                    
                    // Log the specific changes
                    if (!prev_path.empty()) {
                        std::cout << "Path changed from:" << std::endl;
                        for (size_t j = 0; j < prev_path.size(); ++j) {
                            std::cout << prev_path[j];
                            if (j < prev_path.size() - 1) std::cout << " -> ";
                        }
                        std::cout << std::endl << "to:" << std::endl;
                        for (size_t j = 0; j < path.size(); ++j) {
                            std::cout << path[j];
                            if (j < path.size() - 1) std::cout << " -> ";
                        }
                        std::cout << std::endl;
                    }
                }
                
                prev_path = path;
            } else {
                prev_path.clear();
            }
            
            std::cout << "----------------------------------------" << std::endl;
            
            // Small delay to not flood the console too quickly
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    }
};

int main() {
    std::cout << "Dynamic Drone Path Finding Simulation" << std::endl;
    std::cout << "====================================" << std::endl;
    
    int num_drones = 20;
    double simulation_time = 30.0;
    int time_steps = 100;
    
    std::cout << "Initializing with " << num_drones << " drones..." << std::endl;
    DynamicDronePathFinding path_finder(num_drones);
    
    std::cout << "Starting simulation with " << time_steps << " time steps over " 
              << simulation_time << " seconds..." << std::endl;
    std::cout << "----------------------------------------" << std::endl;
    
    path_finder.simulate(simulation_time, time_steps);
    
    std::cout << "Simulation complete." << std::endl;
    
    return 0;
}