#include <iostream>
#include <vector>
#include <queue>
#include <unordered_map>
#include <cmath>
#include <limits>
#include <algorithm>
#include <functional>
#include <set>
#include <random>
#include <chrono>

// Define the Position structure for drone coordinates
struct Position {
    double x, y, z;
    
    Position(double x = 0, double y = 0, double z = 0) : x(x), y(y), z(z) {}
    
    // Calculate Euclidean distance between two positions
    double distanceTo(const Position& other) const {
        return std::sqrt(std::pow(x - other.x, 2) + 
                         std::pow(y - other.y, 2) + 
                         std::pow(z - other.z, 2));
    }
    
    // Overload equality operator for position comparison
    bool operator==(const Position& other) const {
        return x == other.x && y == other.y && z == other.z;
    }
    
    // Overload inequality operator for position comparison
    bool operator!=(const Position& other) const {
        return !(*this == other);
    }
};

// Hash function for Position to use in unordered_map/set
namespace std {
    template<>
    struct hash<Position> {
        size_t operator()(const Position& pos) const {
            return hash<double>()(pos.x) ^ 
                   hash<double>()(pos.y) ^ 
                   hash<double>()(pos.z);
        }
    };
}

// Define the Drone class
class Drone {
private:
    int id;
    Position position;
    double maxSpeed;
    double communicationRange;
    double maxFlightTime;
    double remainingBattery;
    
public:
    Drone(int id, const Position& pos, double speed = 10.0, 
          double commRange = 500.0, double flightTime = 30.0)
        : id(id), position(pos), maxSpeed(speed), 
          communicationRange(commRange), maxFlightTime(flightTime),
          remainingBattery(flightTime) {}
    
    int getId() const { return id; }
    Position getPosition() const { return position; }
    double getMaxSpeed() const { return maxSpeed; }
    double getCommunicationRange() const { return communicationRange; }
    double getRemainingBattery() const { return remainingBattery; }
    
    void setPosition(const Position& pos) { position = pos; }
    
    void updateBattery(double timeElapsed) {
        remainingBattery -= timeElapsed;
        if (remainingBattery < 0) remainingBattery = 0;
    }
    
    bool canCommunicateWith(const Drone& other) const {
        return position.distanceTo(other.getPosition()) <= communicationRange;
    }
    
    bool hasEnoughBattery(double time) const {
        return remainingBattery >= time;
    }
};

// Define a Grid for obstacle representation and path planning
class Grid {
private:
    int width, height, depth;
    std::vector<std::vector<std::vector<bool>>> obstacles;
    double cellSize;
    
public:
    Grid(int w, int h, int d, double size = 10.0) 
        : width(w), height(h), depth(d), cellSize(size) {
        obstacles.resize(width, std::vector<std::vector<bool>>(
            height, std::vector<bool>(depth, false)));
    }
    
    void addObstacle(int x, int y, int z) {
        if (isValidCell(x, y, z)) {
            obstacles[x][y][z] = true;
        }
    }
    
    void addObstacle(const Position& pos) {
        int x = static_cast<int>(pos.x / cellSize);
        int y = static_cast<int>(pos.y / cellSize);
        int z = static_cast<int>(pos.z / cellSize);
        addObstacle(x, y, z);
    }
    
    bool isObstacle(int x, int y, int z) const {
        return isValidCell(x, y, z) && obstacles[x][y][z];
    }
    
    bool isObstacle(const Position& pos) const {
        int x = static_cast<int>(pos.x / cellSize);
        int y = static_cast<int>(pos.y / cellSize);
        int z = static_cast<int>(pos.z / cellSize);
        return isObstacle(x, y, z);
    }
    
    bool isValidCell(int x, int y, int z) const {
        return x >= 0 && x < width && y >= 0 && y < height && z >= 0 && z < depth;
    }
    
    Position cellToPosition(int x, int y, int z) const {
        return Position((x + 0.5) * cellSize, (y + 0.5) * cellSize, (z + 0.5) * cellSize);
    }
    
    std::tuple<int, int, int> positionToCell(const Position& pos) const {
        int x = static_cast<int>(pos.x / cellSize);
        int y = static_cast<int>(pos.y / cellSize);
        int z = static_cast<int>(pos.z / cellSize);
        return std::make_tuple(x, y, z);
    }
    
    int getWidth() const { return width; }
    int getHeight() const { return height; }
    int getDepth() const { return depth; }
    double getCellSize() const { return cellSize; }
};

// AODV routing protocol implementation
class AODV {
public:
    struct RoutingTableEntry {
        int destinationId;
        int nextHopId;
        int hopCount;
        double sequenceNumber;
        double lifetime;
        
        RoutingTableEntry(int destId, int nextHop, int hops, double seqNum, double life)
            : destinationId(destId), nextHopId(nextHop), hopCount(hops),
              sequenceNumber(seqNum), lifetime(life) {}
    };
    
    struct RouteRequestPacket {
        int sourceId;
        int destinationId;
        int requestId;
        int hopCount;
        double sourceSequenceNumber;
        double destinationSequenceNumber;
        std::vector<int> path;
        
        RouteRequestPacket(int srcId, int destId, int reqId, double srcSeq, double destSeq)
            : sourceId(srcId), destinationId(destId), requestId(reqId),
              hopCount(0), sourceSequenceNumber(srcSeq), 
              destinationSequenceNumber(destSeq) {
            path.push_back(srcId);
        }
    };
    
    struct RouteReplyPacket {
        int sourceId;
        int destinationId;
        int hopCount;
        double destinationSequenceNumber;
        double lifetime;
        std::vector<int> path;
        
        RouteReplyPacket(int srcId, int destId, int hops, double destSeq, double life,
                        const std::vector<int>& routePath)
            : sourceId(srcId), destinationId(destId), hopCount(hops),
              destinationSequenceNumber(destSeq), lifetime(life), path(routePath) {}
    };
    
    struct RouteErrorPacket {
        int sourceId;
        int destinationId;
        
        RouteErrorPacket(int srcId, int destId)
            : sourceId(srcId), destinationId(destId) {}
    };
    
private:
    std::unordered_map<int, RoutingTableEntry> routingTable;
    std::unordered_map<int, double> sequenceNumbers;
    std::unordered_map<std::pair<int, int>, bool, PairHash> processedRequests;
    int droneId;
    
    struct PairHash {
        template <class T1, class T2>
        std::size_t operator() (const std::pair<T1, T2>& pair) const {
            return std::hash<T1>()(pair.first) ^ std::hash<T2>()(pair.second);
        }
    };
    
public:
    AODV(int id) : droneId(id) {
        sequenceNumbers[id] = 1.0;
    }
    
    void updateRoutingTable(const RoutingTableEntry& entry) {
        routingTable[entry.destinationId] = entry;
    }
    
    bool hasRouteToDestination(int destinationId) {
        return routingTable.find(destinationId) != routingTable.end() &&
               routingTable[destinationId].lifetime > getCurrentTime();
    }
    
    int getNextHop(int destinationId) {
        if (hasRouteToDestination(destinationId)) {
            return routingTable[destinationId].nextHopId;
        }
        return -1;
    }
    
    std::vector<int> findPathToDestination(int destinationId, 
                                         const std::vector<Drone>& drones) {
        if (!hasRouteToDestination(destinationId)) {
            initiateRouteDiscovery(destinationId, drones);
        }
        
        if (!hasRouteToDestination(destinationId)) {
            return {}; // No path found
        }
        
        // Reconstruct the path
        std::vector<int> path;
        int currentId = droneId;
        path.push_back(currentId);
        
        while (currentId != destinationId) {
            currentId = getNextHop(destinationId);
            if (currentId == -1) {
                return {}; // Path broken
            }
            path.push_back(currentId);
        }
        
        return path;
    }
    
    void initiateRouteDiscovery(int destinationId, const std::vector<Drone>& drones) {
        sequenceNumbers[droneId] += 1.0;
        
        RouteRequestPacket rreq(
            droneId, 
            destinationId, 
            static_cast<int>(sequenceNumbers[droneId]),
            sequenceNumbers[droneId],
            sequenceNumbers[destinationId]
        );
        
        processRouteRequest(rreq, drones);
    }
    
    void processRouteRequest(const RouteRequestPacket& rreq, 
                            const std::vector<Drone>& drones) {
        std::pair<int, int> requestKey(rreq.sourceId, rreq.requestId);
        
        // Check if we've already processed this request
        if (processedRequests.find(requestKey) != processedRequests.end()) {
            return;
        }
        processedRequests[requestKey] = true;
        
        // Update the sequence number for the source
        if (sequenceNumbers.find(rreq.sourceId) == sequenceNumbers.end() ||
            sequenceNumbers[rreq.sourceId] < rreq.sourceSequenceNumber) {
            sequenceNumbers[rreq.sourceId] = rreq.sourceSequenceNumber;
        }
        
        // Create a reverse route to the source
        RoutingTableEntry reverseRoute(
            rreq.sourceId,
            rreq.path.back(), // The last node in the path
            rreq.hopCount,
            rreq.sourceSequenceNumber,
            getCurrentTime() + 10.0 // Lifetime of 10 seconds
        );
        updateRoutingTable(reverseRoute);
        
        // If this drone is the destination or has a fresh enough route to the destination
        if (droneId == rreq.destinationId || 
            (hasRouteToDestination(rreq.destinationId) && 
             routingTable[rreq.destinationId].sequenceNumber >= rreq.destinationSequenceNumber)) {
            
            std::vector<int> replyPath = rreq.path;
            std::reverse(replyPath.begin(), replyPath.end());
            
            RouteReplyPacket rrep(
                droneId,
                rreq.sourceId,
                (droneId == rreq.destinationId) ? 0 : routingTable[rreq.destinationId].hopCount,
                (droneId == rreq.destinationId) ? 
                    sequenceNumbers[droneId] : routingTable[rreq.destinationId].sequenceNumber,
                getCurrentTime() + 20.0, // Lifetime of 20 seconds
                replyPath
            );
            
            processRouteReply(rrep, drones);
            return;
        }
        
        // Forward the RREQ to neighbors
        RouteRequestPacket forwardRreq = rreq;
        forwardRreq.hopCount++;
        forwardRreq.path.push_back(droneId);
        
        // Find neighbors to forward to
        for (const auto& drone : drones) {
            if (drone.getId() != droneId && canCommunicateWith(drone, drones)) {
                // In a real implementation, this would be a network transmission
                // Here we're directly passing the packet
                // In a real system, we'd need to handle this asynchronously
            }
        }
    }
    
    void processRouteReply(const RouteReplyPacket& rrep, const std::vector<Drone>& drones) {
        // Update the routing table for the destination
        RoutingTableEntry destRoute(
            rrep.destinationId,
            rrep.path[1], // Next hop in the path
            rrep.hopCount,
            rrep.destinationSequenceNumber,
            rrep.lifetime
        );
        updateRoutingTable(destRoute);
        
        // If this drone is not the source of the route discovery, forward the RREP
        if (droneId != rrep.sourceId) {
            // Find the next hop to the source
            int nextHopToSource = -1;
            for (size_t i = 0; i < rrep.path.size(); i++) {
                if (rrep.path[i] == droneId && i < rrep.path.size() - 1) {
                    nextHopToSource = rrep.path[i + 1];
                    break;
                }
            }
            
            if (nextHopToSource != -1) {
                // Forward the RREP to the next hop
                // In a real implementation, this would be a network transmission
                // Here we're simulating by direct method calls
            }
        }
    }
    
    void processRouteError(const RouteErrorPacket& rerr) {
        // Remove the route to the destination from the routing table
        routingTable.erase(rerr.destinationId);
        
        // Forward the RERR to neighbors that have a route to the destination through this drone
        // This part is simplified
    }
    
    bool canCommunicateWith(const Drone& other, const std::vector<Drone>& drones) {
        // Find this drone
        const Drone* thisDrone = nullptr;
        for (const auto& drone : drones) {
            if (drone.getId() == droneId) {
                thisDrone = &drone;
                break;
            }
        }
        
        if (!thisDrone) return false;
        
        return thisDrone->canCommunicateWith(other);
    }
    
    // Utility function to get current time
    static double getCurrentTime() {
        auto now = std::chrono::high_resolution_clock::now();
        auto duration = now.time_since_epoch();
        return std::chrono::duration_cast<std::chrono::seconds>(duration).count();
    }
};

// REPS Ramalingam Algorithm implementation
class REPSRamalingam {
private:
    const Grid& grid;
    
    // REPS algorithm constants
    const double alpha = 0.8;    // Search focus parameter
    const double beta = 0.2;     // Exploration factor
    const int maxIterations = 1000;
    
    struct Node {
        Position position;
        double gCost;      // Cost from start
        double hCost;      // Heuristic cost to goal
        double rCost;      // REPS cost function
        Node* parent;
        
        Node(const Position& pos, double g = 0, double h = 0, Node* p = nullptr)
            : position(pos), gCost(g), hCost(h), rCost(g + h), parent(p) {}
        
        // Sort nodes by REPS cost
        bool operator>(const Node& other) const {
            return rCost > other.rCost;
        }
    };
    
    // Calculate heuristic (Euclidean distance)
    double calculateHeuristic(const Position& a, const Position& b) const {
        return a.distanceTo(b);
    }
    
    // Calculate the REPS cost function
    double calculateREPSCost(double gCost, double hCost, std::mt19937& rng) const {
        std::uniform_real_distribution<double> dist(0.0, 1.0);
        double randomFactor = dist(rng);
        return alpha * gCost + (1 - alpha) * hCost + beta * randomFactor;
    }
    
    // Get neighboring positions
    std::vector<Position> getNeighbors(const Position& pos) const {
        std::vector<Position> neighbors;
        double step = grid.getCellSize();
        
        // 6-connected grid (up, down, left, right, forward, backward)
        std::vector<Position> directions = {
            Position(step, 0, 0),    // Right
            Position(-step, 0, 0),   // Left
            Position(0, step, 0),    // Forward
            Position(0, -step, 0),   // Backward
            Position(0, 0, step),    // Up
            Position(0, 0, -step)    // Down
        };
        
        for (const auto& dir : directions) {
            Position neighbor(pos.x + dir.x, pos.y + dir.y, pos.z + dir.z);
            
            // Check if the neighbor is valid and not an obstacle
            auto [nx, ny, nz] = grid.positionToCell(neighbor);
            if (grid.isValidCell(nx, ny, nz) && !grid.isObstacle(nx, ny, nz)) {
                neighbors.push_back(neighbor);
            }
        }
        
        return neighbors;
    }

public:
    REPSRamalingam(const Grid& g) : grid(g) {}
    
    std::vector<Position> findPath(const Position& start, const Position& goal) {
        // Random number generator for REPS
        std::random_device rd;
        std::mt19937 rng(rd());
        
        // Open set (priority queue)
        std::priority_queue<Node*, std::vector<Node*>, std::greater<Node*>> openSet;
        
        // Closed set (visited nodes)
        std::unordered_map<Position, bool> closedSet;
        
        // Cost map
        std::unordered_map<Position, double> gCostMap;
        
        // Start with the initial node
        Node* startNode = new Node(start, 0, calculateHeuristic(start, goal));
        openSet.push(startNode);
        gCostMap[start] = 0;
        
        int iterations = 0;
        
        while (!openSet.empty() && iterations < maxIterations) {
            iterations++;
            
            // Get the node with lowest REPS cost
            Node* current = openSet.top();
            openSet.pop();
            
            // Check if we've reached the goal
            if (current->position.distanceTo(goal) < grid.getCellSize()) {
                // Reconstruct path
                std::vector<Position> path;
                while (current != nullptr) {
                    path.push_back(current->position);
                    current = current->parent;
                }
                std::reverse(path.begin(), path.end());
                
                // Cleanup memory
                // In a real implementation, proper memory management would be needed
                
                return path;
            }
            
            // Mark as visited
            closedSet[current->position] = true;
            
            // Check all neighbors
            for (const auto& neighbor : getNeighbors(current->position)) {
                // Skip if already visited
                if (closedSet.find(neighbor) != closedSet.end()) {
                    continue;
                }
                
                // Calculate cost to this neighbor
                double tentativeGCost = current->gCost + current->position.distanceTo(neighbor);
                
                // Check if this path is better than any previous path to this neighbor
                if (gCostMap.find(neighbor) == gCostMap.end() || tentativeGCost < gCostMap[neighbor]) {
                    // This is a better path
                    gCostMap[neighbor] = tentativeGCost;
                    
                    double hCost = calculateHeuristic(neighbor, goal);
                    double rCost = calculateREPSCost(tentativeGCost, hCost, rng);
                    
                    Node* neighborNode = new Node(neighbor, tentativeGCost, hCost, current);
                    neighborNode->rCost = rCost;
                    openSet.push(neighborNode);
                }
            }
        }
        
        // No path found
        return std::vector<Position>();
    }
};

// Integration of REPS with AODV
class REPSAODVRouter {
private:
    std::vector<Drone> drones;
    Grid grid;
    std::unordered_map<int, AODV> routingProtocols;
    
public:
    REPSAODVRouter(const std::vector<Drone>& drs, const Grid& g) 
        : drones(drs), grid(g) {
        // Initialize AODV protocol for each drone
        for (const auto& drone : drones) {
            routingProtocols[drone.getId()] = AODV(drone.getId());
        }
    }
    
    std::vector<Position> findPath(int sourceId, int destinationId, 
                                   const Position& startPos, const Position& goalPos) {
        // Step 1: Find network route using AODV
        std::vector<int> networkPath;
        if (sourceId != destinationId) {
            networkPath = routingProtocols[sourceId].findPathToDestination(destinationId, drones);
            if (networkPath.empty()) {
                return {}; // No network path found
            }
        } else {
            networkPath.push_back(sourceId); // Path to self
        }
        
        // Step 2: Use REPS Ramalingam algorithm for spatial path finding
        REPSRamalingam pathFinder(grid);
        return pathFinder.findPath(startPos, goalPos);
    }
    
    // Update drone positions and maintain network state
    void update(double timeStep) {
        // Update drone positions based on their movement
        // In a real system, this would integrate with the physical drone control
        
        // Update AODV routing tables
        // This would involve detecting broken links and initiating route discovery
    }
};

int main() {
    // Create a 3D grid for the environment
    Grid grid(100, 100, 10, 10.0);
    
    // Add some obstacles to the grid
    for (int i = 30; i < 70; i++) {
        for (int j = 40; j < 50; j++) {
            grid.addObstacle(i, j, 2);
        }
    }
    
    // Create drones
    std::vector<Drone> drones;
    drones.push_back(Drone(1, Position(10, 10, 20)));
    drones.push_back(Drone(2, Position(30, 30, 25)));
    drones.push_back(Drone(3, Position(50, 50, 30)));
    drones.push_back(Drone(4, Position(70, 70, 35)));
    drones.push_back(Drone(5, Position(90, 90, 40)));
    
    // Initialize the REPS-AODV router
    REPSAODVRouter router(drones, grid);
    
    // Define start and goal positions
    Position startPos(5, 5, 20);
    Position goalPos(95, 95, 40);
    
    // Find a path from drone 1 to drone 5
    std::vector<Position> path = router.findPath(1, 5, startPos, goalPos);
    
    // Print the path
    if (path.empty()) {
        std::cout << "No path found!" << std::endl;
    } else {
        std::cout << "Path found with " << path.size() << " waypoints:" << std::endl;
        for (size_t i = 0; i < path.size(); i++) {
            std::cout << "Waypoint " << i + 1 << ": (" 
                     << path[i].x << ", " << path[i].y << ", " << path[i].z << ")" << std::endl;
        }
    }
    
    return 0;
}