#define _USE_MATH_DEFINES // For M_PI
#include <iostream>
#include <vector>
#include <cmath>
#include <complex> // For std::complex
#include <random>
#include <map>
#include <set>
#include <queue>
#include <limits>
#include <chrono>
#include <fstream>
#include <string>
#include <sstream>
#include <algorithm>
#include <functional> // For std::greater
#include <unordered_map> // For RR data structures
#include <unordered_set> // For RR data structures
#include <filesystem> // For checking/creating directories

// --- Drone Structure ---
struct Drone {
    int id;
    double x;
    double y;
    double z;
    double battery_capacity = 5000.0; // mAh
    double battery_level = 100.0; // Percentage
    enum State { ACTIVE, GROUNDED, LANDED, ASCENDING, DESCENDING } state = ACTIVE;
    double target_x, target_y, target_z;
    bool is_base_station = false; // Flag for base stations

    // Channel model parameters
    double k_factor;
    double path_loss_exp;
    std::vector<std::complex<double>> channel; // Store channel coefficients

    Drone(int id, double x, double y, double z, double capacity = 5000.0)
        : id(id), x(x), y(y), z(z), battery_capacity(capacity), battery_level(100.0), state(ACTIVE),
          target_x(x), target_y(y), target_z(z), is_base_station(false), k_factor(0), path_loss_exp(2.0) {}

    double distanceTo(const Drone& other) const {
        return std::sqrt(std::pow(x - other.x, 2) + std::pow(y - other.y, 2) + std::pow(z - other.z, 2));
    }
};

// --- Routing Packet Structure ---
struct RoutingPacket {
    int source_id;
    int destination_id;
    int current_node_id;
    double creation_time;
    double last_hop_time;
    bool delivered;
    std::vector<int> path; // Track the path taken

    RoutingPacket(int src, int dest, double time)
        : source_id(src), destination_id(dest), current_node_id(src),
          creation_time(time), last_hop_time(time), delivered(false) {
        path.push_back(src);
    }
};

// --- Routing Metrics Structure ---
struct RoutingMetrics {
    int total_packets_delivered = 0;
    double total_end_to_end_latency = 0.0;
    double total_astar_execution_time = 0.0; // Will rename/repurpose for RR
    int total_astar_executions = 0; // Will rename/repurpose for RR
    int total_route_discoveries = 0; // AODV discoveries
    int total_route_failures = 0; // AODV failures (RERR)
    int total_rreq_sent = 0;
    int total_rrep_sent = 0;
    int total_rerr_sent = 0;

    double getAverageLatency() const {
        return total_packets_delivered > 0 ? total_end_to_end_latency / total_packets_delivered : 0.0;
    }

    double getAverageRRTime() const {
        return total_astar_executions > 0 ? total_astar_execution_time / total_astar_executions : 0.0;
    }

    double getAverageRouteDiscoveryTime() const {
        return total_route_discoveries > 0 ? (double)(total_rreq_sent + total_rrep_sent) / total_route_discoveries : 0.0;
    }
};

// --- AODV Structures ---
struct AODVRouteEntry {
    int destination_id;
    int next_hop_id;
    int hop_count;
    int sequence_number;
    double lifetime; // Timestamp when the route expires
    bool valid;

    AODVRouteEntry(int dest = -1, int next = -1, int hops = -1, int seq = -1, double life = 0.0, bool v = true)
        : destination_id(dest), next_hop_id(next), hop_count(hops),
          sequence_number(seq), lifetime(life), valid(v) {}
};

struct AODVRouteRequest {
    int rreq_id;
    int source_id;
    int destination_id;
    int source_sequence;
    int destination_sequence; // Last known sequence number for the destination
    int hop_count;
    double timestamp; // Time when RREQ was created/forwarded

    AODVRouteRequest(int id, int src, int dest, int src_seq, int dest_seq, int hops, double time)
        : rreq_id(id), source_id(src), destination_id(dest), source_sequence(src_seq),
          destination_sequence(dest_seq), hop_count(hops), timestamp(time) {}
};

struct AODVRouteReply {
    int source_id;
    int destination_id;
    int destination_sequence;
    int hop_count;
    double lifetime; // How long the route is valid
    double timestamp; // Time when RREP was created/forwarded

    AODVRouteReply(int src, int dest, int dest_seq, int hops, double life, double time)
        : source_id(src), destination_id(dest), destination_sequence(dest_seq),
          hop_count(hops), lifetime(life), timestamp(time) {}
};

struct AODVRouteError {
    int source_id; // Node detecting the error
    int destination_id; // Unreachable destination
    int sequence_number; // Sequence number of the unreachable destination
    double timestamp; // Time when RERR was created

    AODVRouteError(int src, int dest, int seq, double time)
        : source_id(src), destination_id(dest), sequence_number(seq), timestamp(time) {}
};

// --- Simulator Class ---
class DroneNetworkSimulator {
public:
    DroneNetworkSimulator(int numDrones, double width, double height, double range,
                          double min_h, double max_h)
        : worldWidth(width), worldHeight(height), worldMinHeight(min_h), worldMaxHeight(max_h),
          communicationRange(range), simulationTime(0.0), rng(std::random_device{}()) {

        for (int i = 0; i < numDrones; ++i) {
            std::uniform_real_distribution<> disX(0, worldWidth);
            std::uniform_real_distribution<> disY(0, worldHeight);
            std::uniform_real_distribution<> disZ(worldMinHeight, worldMaxHeight);
            drones.emplace_back(i + 1, disX(rng), disY(rng), disZ(rng)); // IDs start from 1
            setRandomTarget(drones.back());
            sequence_numbers[i + 1] = 0; // Initialize AODV sequence numbers
        }

        if (numDrones >= 2) {
            transmitter_base_id = 1;
            receiver_base_id = 2;
            drones[0].is_base_station = true;
            drones[1].is_base_station = true;
            drones[0].x = 10.0; drones[0].y = height / 2.0; drones[0].z = min_h;
            drones[1].x = width - 10.0; drones[1].y = height / 2.0; drones[1].z = min_h;

            // Set base stations to ACTIVE instead of LANDED to allow packet forwarding
            drones[0].state = Drone::ACTIVE;
            drones[1].state = Drone::ACTIVE;

            // Set infinite battery for base stations
            drones[0].battery_capacity = std::numeric_limits<double>::infinity();
            drones[0].battery_level = 100.0;
            drones[1].battery_capacity = std::numeric_limits<double>::infinity();
            drones[1].battery_level = 100.0;

            std::cout << "Transmitter Base Station ID: " << transmitter_base_id << std::endl;
            std::cout << "Receiver Base Station ID: " << receiver_base_id << std::endl;
        } else {
            transmitter_base_id = -1;
            receiver_base_id = -1;
            std::cerr << "Warning: Less than 2 drones, cannot designate base stations." << std::endl;
        }

        fc = 2.4e9;
        v = 10.0;
        c = 3e8;
        ts = 0.01;
        initializeChannelModel();

        initCSILogging();
        initMetricsLogging();

        updateConnectivity();

        if (transmitter_base_id != -1) {
             initializeRR(transmitter_base_id);
        } else if (!drones.empty()) {
             initializeRR(drones[0].id);
             std::cerr << "Warning: No transmitter base station, initializing RR from drone " << drones[0].id << std::endl;
        }

        std::cout << "Simulator initialized with " << numDrones << " drones." << std::endl;
    }

    ~DroneNetworkSimulator() {
        closeCSILogging();
        closeMetricsLogging();
    }

    void runSimulation(int numSteps, double timeStep) {
        std::cout << "\n--- Starting Simulation ---" << std::endl;

        // Debug state before starting
        debugState();

        try {
            for (int i = 0; i < numSteps; ++i) {
                std::cout << "\n--- Simulation Step " << i + 1 << " (Time: " << simulationTime << ") ---" << std::endl;

                try {
                    tick(timeStep);

                    // Periodically create packets between base stations
                    if (transmitter_base_id != -1 && receiver_base_id != -1 && i % 50 == 0) {
                        createPacket(transmitter_base_id, receiver_base_id);
                    }

                    // Print status less frequently
                    if (i % 10 == 0) {
                        printStatus();
                        printRoutingMetrics();
                    }

                    // Ensure metrics are logged every 10 steps
                    if (i % 10 == 0) {
                        try {
                            logMetricsData();
                        } catch (const std::exception& e) {
                            std::cerr << "Error in logMetricsData: " << e.what() << std::endl;
                        }
                    }
                } catch (const std::exception& e) {
                    std::cerr << "Error in simulation step " << i + 1 << ": " << e.what() << std::endl;
                    // Continue to next step despite error
                }
            }
        } catch (const std::exception& e) {
            std::cerr << "Fatal error in runSimulation: " << e.what() << std::endl;
        }

        std::cout << "\n--- Simulation Finished ---" << std::endl;
        printRoutingMetrics(); // Final metrics
    }

private:
    std::vector<Drone> drones;
    int transmitter_base_id = -1;
    int receiver_base_id = -1;
    double simulationTime;
    double worldWidth;
    double worldHeight;
    double worldMinHeight;
    double worldMaxHeight;
    double communicationRange;
    std::map<int, std::vector<int>> connectivityMap;
    std::mt19937 rng;

    double a1 = 0.5, b1 = 150;
    double alpha_zero = 0.1, alpha_pi_by_2 = 0.5;
    double k_zero = 10, k_pi_by_2 = 1;
    double fc, v, c, ts;

    double transmit_power_dBm = 20.0;
    double noise_floor_dBm = -90.0;
    double snr_threshold_dB = 5.0;

    std::ofstream csi_log_file;
    std::ofstream metrics_log_file;

    std::vector<RoutingPacket> active_packets;
    RoutingMetrics routing_metrics;

    const double PACKET_PROCESSING_POWER = 0.01;
    const double AODV_ROUTE_DISCOVERY_TIMEOUT = 2.0;
    const double AODV_ROUTE_LIFETIME = 10.0;
    const double AODV_ACTIVE_ROUTE_TIMEOUT = 5.0;
    const int AODV_TTL_START = 1;
    const int AODV_TTL_INCREMENT = 2;
    const int AODV_TTL_THRESHOLD = 7;
    const int AODV_NET_DIAMETER = 35;
    std::map<int, std::map<int, AODVRouteEntry>> route_tables;
    std::map<int, int> sequence_numbers;
    std::vector<AODVRouteRequest> pending_route_requests;
    std::vector<AODVRouteReply> pending_route_replies;
    std::vector<AODVRouteError> pending_route_errors;
    std::map<int, std::map<std::pair<int, int>, double>> rreq_seen;

    std::unordered_map<int, double> dist;
    std::unordered_map<int, int> pred;
    using PqElement = std::pair<double, int>;
    std::priority_queue<PqElement, std::vector<PqElement>, std::greater<PqElement>> pq;
    int rr_source_id = -1;
    std::unordered_map<int, std::unordered_map<int, double>> graph_weights;

    void initMetricsLogging(const std::string& filename = "new_op_metrics_log.csv") {
        // Add error reporting
        try {
            // Ensure absolute path if needed
            std::string fullPath = std::filesystem::current_path().string() + "/" + filename;
            std::cout << "Attempting to open metrics log file at: " << fullPath << std::endl;

            metrics_log_file.open(filename);
            if (metrics_log_file.is_open()) {
                metrics_log_file << "Time,PacketsDelivered,AvgLatency,AvgRRTime,AvgAODVDiscoveryTime,RREQSent,RREPSent,RERRSent,RouteDiscoveries,RouteFailures\n";
                metrics_log_file.flush();
                std::cout << "Metrics logging initialized to " << filename << std::endl;
            } else {
                std::cerr << "Error opening metrics log file: " << filename << std::endl;
                std::cerr << "Current directory: " << std::filesystem::current_path() << std::endl;
                perror("File open error"); // Print system error message
            }
        } catch (const std::exception& e) {
            std::cerr << "Exception in initMetricsLogging: " << e.what() << std::endl;
        }
    }

    void logMetricsData() {
         if (!metrics_log_file.is_open()) {
             std::cerr << "Warning: Metrics log file not open when trying to log data at time " << simulationTime << std::endl;
             return;
         }

         // Calculate metrics
         double avgLatency = routing_metrics.getAverageLatency();
         double avgRRTime = routing_metrics.getAverageRRTime();
         double avgAODVDiscoveryTime = routing_metrics.getAverageRouteDiscoveryTime();

         // Log with detailed output
         std::cout << "Logging metrics at time " << simulationTime << ":" << std::endl;
         std::cout << "  Packets Delivered: " << routing_metrics.total_packets_delivered << std::endl;
         std::cout << "  Avg Latency: " << avgLatency << std::endl;
         std::cout << "  Avg RR Time: " << avgRRTime << std::endl;
         std::cout << "  Avg AODV Discovery Time: " << avgAODVDiscoveryTime << std::endl;
         std::cout << "  RREQ Sent: " << routing_metrics.total_rreq_sent << std::endl;
         std::cout << "  RREP Sent: " << routing_metrics.total_rrep_sent << std::endl;
         std::cout << "  RERR Sent: " << routing_metrics.total_rerr_sent << std::endl;
         std::cout << "  Route Discoveries: " << routing_metrics.total_route_discoveries << std::endl;
         std::cout << "  Route Failures: " << routing_metrics.total_route_failures << std::endl;

         // Write to file
         metrics_log_file << simulationTime << ","
                          << routing_metrics.total_packets_delivered << ","
                          << avgLatency << ","
                          << avgRRTime << ","
                          << avgAODVDiscoveryTime << ","
                          << routing_metrics.total_rreq_sent << ","
                          << routing_metrics.total_rrep_sent << ","
                          << routing_metrics.total_rerr_sent << ","
                          << routing_metrics.total_route_discoveries << ","
                          << routing_metrics.total_route_failures << "\n";

         // Ensure data is written to disk
         metrics_log_file.flush();
    }

    void closeMetricsLogging() {
        if (metrics_log_file.is_open()) {
            metrics_log_file.close();
            std::cout << "Metrics logging closed." << std::endl;
        }
    }

    void initCSILogging(const std::string& filename = "new_op_csi_data.csv") {
        // Add error reporting
        try {
            // Ensure absolute path if needed
            std::string fullPath = std::filesystem::current_path().string() + "/" + filename;
            std::cout << "Attempting to open CSI log file at: " << fullPath << std::endl;

            csi_log_file.open(filename);
            if (csi_log_file.is_open()) {
                csi_log_file << "Time,SourceID,DestID,Distance,SNR_dB,PathLoss_dB,KFactor,PathLossExp\n";
                csi_log_file.flush();
                std::cout << "CSI logging initialized to " << filename << std::endl;
            } else {
                std::cerr << "Error opening CSI log file: " << filename << std::endl;
                std::cerr << "Current directory: " << std::filesystem::current_path() << std::endl;
                perror("File open error"); // Print system error message
            }
        } catch (const std::exception& e) {
            std::cerr << "Exception in initCSILogging: " << e.what() << std::endl;
        }
    }

    void closeCSILogging() {
         if (csi_log_file.is_open()) {
            csi_log_file.close();
             std::cout << "CSI logging closed." << std::endl;
        }
    }

    void tick(double timeStep) {
        simulationTime += timeStep;

        moveDrones(timeStep);
        enforceBounds();
        manageDroneLifecycle(timeStep);

        updateConnectivity();

        processAODVMessages();

        processPackets(timeStep);

        for (auto& drone : drones) {
            double power = calculatePowerConsumption(drone, timeStep);
            updateBattery(drone, power, timeStep);
        }

        logCSIData();

        cleanupAODVState();
    }

    void setRandomTarget(Drone& drone) {
         if (drone.is_base_station) return;
         std::uniform_real_distribution<> disX(0, worldWidth);
         std::uniform_real_distribution<> disY(0, worldHeight);
         std::uniform_real_distribution<> disZ(worldMinHeight, worldMaxHeight);
         drone.target_x = disX(rng);
         drone.target_y = disY(rng);
         drone.target_z = disZ(rng);
    }

    void moveDrones(double timeStep) {
        const double speed = 5.0;
        for (auto& drone : drones) {
            if (drone.is_base_station || drone.state == Drone::LANDED || drone.state == Drone::GROUNDED) continue;

            double dx = drone.target_x - drone.x;
            double dy = drone.target_y - drone.y;
            double dz = drone.target_z - drone.z;
            double dist_to_target = std::sqrt(dx*dx + dy*dy + dz*dz);

            if (dist_to_target < speed * timeStep) {
                drone.x = drone.target_x;
                drone.y = drone.target_y;
                drone.z = drone.target_z;
                setRandomTarget(drone);
            } else {
                drone.x += (dx / dist_to_target) * speed * timeStep;
                drone.y += (dy / dist_to_target) * speed * timeStep;
                drone.z += (dz / dist_to_target) * speed * timeStep;
            }
        }
    }

    void enforceBounds() {
        for (auto& drone : drones) {
             if (drone.is_base_station) continue;
             drone.x = std::max(0.0, std::min(worldWidth, drone.x));
             drone.y = std::max(0.0, std::min(worldHeight, drone.y));
             drone.z = std::max(worldMinHeight, std::min(worldMaxHeight, drone.z));
        }
    }

    void updateConnectivity() {
        std::cout << "Updating connectivity and RR..." << std::endl;
        auto old_connectivityMap = connectivityMap;
        std::map<int, std::vector<int>> new_connectivityMap;
        std::unordered_map<int, std::unordered_map<int, double>> new_graph_weights;

        std::vector<std::pair<int, int>> edges_added;
        std::vector<std::pair<int, int>> edges_removed;
        std::vector<std::tuple<int, int, double>> edges_weight_decreased;
        std::vector<std::tuple<int, int, double>> edges_weight_increased;

        // Ensure base stations are always connected if they exist
        if (transmitter_base_id != -1 && receiver_base_id != -1) {
            double base_dist = 0.0;
            for (size_t i = 0; i < drones.size(); ++i) {
                if (drones[i].id == transmitter_base_id) {
                    for (size_t j = 0; j < drones.size(); ++j) {
                        if (drones[j].id == receiver_base_id) {
                            base_dist = drones[i].distanceTo(drones[j]);
                            break;
                        }
                    }
                    break;
                }
            }

            // Force connection between base stations
            new_connectivityMap[transmitter_base_id].push_back(receiver_base_id);
            new_connectivityMap[receiver_base_id].push_back(transmitter_base_id);
            new_graph_weights[transmitter_base_id][receiver_base_id] = base_dist;
            new_graph_weights[receiver_base_id][transmitter_base_id] = base_dist;

            bool was_connected = false;
            auto old_it_u = old_connectivityMap.find(transmitter_base_id);
            if (old_it_u != old_connectivityMap.end()) {
                if (std::find(old_it_u->second.begin(), old_it_u->second.end(), receiver_base_id) != old_it_u->second.end()) {
                    was_connected = true;
                }
            }

            if (!was_connected) {
                edges_added.push_back({transmitter_base_id, receiver_base_id});
                edges_added.push_back({receiver_base_id, transmitter_base_id});
                std::cout << "  Edge ADDED (FORCED): " << transmitter_base_id << " <-> " << receiver_base_id
                          << " (Dist: " << base_dist << ")" << std::endl;
            }
        }

        for (size_t i = 0; i < drones.size(); ++i) {
            if (drones[i].state == Drone::GROUNDED) continue;

            for (size_t j = i + 1; j < drones.size(); ++j) {
                if (drones[j].state == Drone::GROUNDED) continue;

                // Skip if this is the base station pair (already handled)
                if ((drones[i].id == transmitter_base_id && drones[j].id == receiver_base_id) ||
                    (drones[i].id == receiver_base_id && drones[j].id == transmitter_base_id)) {
                    continue;
                }

                double dist = drones[i].distanceTo(drones[j]);
                double path_loss = calculatePathLoss(dist);
                double received_power_dBm = transmit_power_dBm - path_loss;
                double snr_dB = received_power_dBm - noise_floor_dBm;

                bool connected = (snr_dB >= snr_threshold_dB);
                int u = drones[i].id;
                int v = drones[j].id;
                double current_weight = dist;

                bool was_connected = false;
                auto old_it_u = old_connectivityMap.find(u);
                if (old_it_u != old_connectivityMap.end()) {
                    if (std::find(old_it_u->second.begin(), old_it_u->second.end(), v) != old_it_u->second.end()) {
                        was_connected = true;
                    }
                }

                if (connected) {
                    new_connectivityMap[u].push_back(v);
                    new_connectivityMap[v].push_back(u);
                    new_graph_weights[u][v] = current_weight;
                    new_graph_weights[v][u] = current_weight;

                    if (!was_connected) {
                        edges_added.push_back({u, v});
                        edges_added.push_back({v, u});
                        std::cout << "  Edge ADDED: " << u << " <-> " << v << " (SNR: " << snr_dB << " dB, Dist: " << dist << ")" << std::endl;
                    } else {
                        double old_weight = graph_weights.count(u) && graph_weights[u].count(v) ? graph_weights[u][v] : std::numeric_limits<double>::infinity();
                        if (current_weight < old_weight) {
                            edges_weight_decreased.emplace_back(u, v, current_weight);
                            edges_weight_decreased.emplace_back(v, u, current_weight);
                             std::cout << "  Edge DECREASED: " << u << " <-> " << v << " (New W: " << current_weight << ", Old W: " << old_weight << ")" << std::endl;
                        } else if (current_weight > old_weight) {
                             edges_weight_increased.emplace_back(u, v, current_weight);
                             edges_weight_increased.emplace_back(v, u, current_weight);
                             std::cout << "  Edge INCREASED: " << u << " <-> " << v << " (New W: " << current_weight << ", Old W: " << old_weight << ")" << std::endl;
                        }
                    }
                } else {
                    if (was_connected) {
                        edges_removed.push_back({u, v});
                        edges_removed.push_back({v, u});
                        std::cout << "  Edge REMOVED: " << u << " <-> " << v << " (SNR: " << snr_dB << " dB, Dist: " << dist << ")" << std::endl;
                    }
                }
            }
        }

        connectivityMap = new_connectivityMap;
        graph_weights = new_graph_weights;

        bool updated = false;

        for (const auto& edge : edges_added) {
            double weight = graph_weights[edge.first][edge.second];
            if (updateEdgeDecrease(edge.first, edge.second, weight)) updated = true;
        }
        for (const auto& edge_data : edges_weight_decreased) {
             if (updateEdgeDecrease(std::get<0>(edge_data), std::get<1>(edge_data), std::get<2>(edge_data))) updated = true;
        }

        for (const auto& edge : edges_removed) {
             if (updateEdgeIncrease(edge.first, edge.second)) updated = true;
        }
         for (const auto& edge_data : edges_weight_increased) {
             if (updateEdgeIncrease(std::get<0>(edge_data), std::get<1>(edge_data))) updated = true;
         }

        if (updated) {
            std::cout << "  Processing RR updates..." << std::endl;
            processUpdates();
            std::cout << "  RR updates processed." << std::endl;
        } else {
             std::cout << "  No RR updates triggered." << std::endl;
        }

        for (auto& node_routes_pair : route_tables) {
            int node_id = node_routes_pair.first;
            auto& node_routes = node_routes_pair.second;
            std::vector<int> broken_next_hops;

            for (auto const& [dest_id, route_entry] : node_routes) {
                if (route_entry.valid) {
                    int next_hop = route_entry.next_hop_id;
                    bool link_exists = false;
                    auto conn_it = connectivityMap.find(node_id);
                    if (conn_it != connectivityMap.end()) {
                        if (std::find(conn_it->second.begin(), conn_it->second.end(), next_hop) != conn_it->second.end()) {
                            link_exists = true;
                        }
                    }
                    if (!link_exists) {
                        broken_next_hops.push_back(next_hop);
                        node_routes[dest_id].valid = false;
                        node_routes[dest_id].lifetime = simulationTime;
                         std::cout << "AODV link broken for Node " << node_id << " -> Next Hop " << next_hop << " (for Dest " << dest_id << ")" << std::endl;
                    }
                }
            }
            std::sort(broken_next_hops.begin(), broken_next_hops.end());
            broken_next_hops.erase(std::unique(broken_next_hops.begin(), broken_next_hops.end()), broken_next_hops.end());
            for (int broken_hop : broken_next_hops) {
                 generateRERR(node_id, broken_hop);
            }
        }
    }

    void initializeChannelModel() {
         for (auto& drone : drones) {
            double elevation_angle = std::atan(drone.z / std::sqrt(drone.x*drone.x + drone.y*drone.y));

            double p_los = 1.0 / (1.0 + a1 * std::exp(-b1 * (elevation_angle * 180.0 / M_PI - a1)));

            double angle_deg = elevation_angle * 180.0 / M_PI;
            drone.path_loss_exp = alpha_zero + (alpha_pi_by_2 - alpha_zero) * (angle_deg / 90.0);
            drone.k_factor = k_zero + (k_pi_by_2 - k_zero) * (angle_deg / 90.0);
        }
    }

    void generateChannelCoefficients(Drone& drone, int num_realizations = 1000) {
         double p_los_plus_nlos = 1.0;
        double sigma = p_los_plus_nlos / std::sqrt(2.0 * (drone.k_factor + 1.0));
        double s = std::sqrt(drone.k_factor / (drone.k_factor + 1.0) * p_los_plus_nlos);

        std::normal_distribution<double> normal_dist(0.0, 1.0);

        double f_doppler = v * fc / c;

        drone.channel.resize(num_realizations);
        for (int i = 0; i < num_realizations; ++i) {
            double x_nlos = normal_dist(rng);
            double y_nlos = normal_dist(rng);

            double phase_los = 2.0 * M_PI * f_doppler * i * ts;

            std::complex<double> h_los(s * std::cos(phase_los), s * std::sin(phase_los));
            std::complex<double> h_nlos(sigma * x_nlos, sigma * y_nlos);

            drone.channel[i] = h_los + h_nlos;
        }
    }

    void printStatus() const {
         std::cout << "Current State (Time: " << simulationTime << "):" << std::endl;
        for (const auto& drone : drones) {
            std::cout << "  Drone " << drone.id
                      << " Pos: (" << drone.x << ", " << drone.y << ", " << drone.z << ")"
                      << " Target: (" << drone.target_x << ", " << drone.target_y << ", " << drone.target_z << ")"
                      << " Batt: " << drone.battery_level << "%"
                      << " State: ";
            switch (drone.state) {
                case Drone::ACTIVE: std::cout << "ACTIVE"; break;
                case Drone::GROUNDED: std::cout << "GROUNDED"; break;
                case Drone::LANDED: std::cout << "LANDED"; break;
                case Drone::ASCENDING: std::cout << "ASCENDING"; break;
                case Drone::DESCENDING: std::cout << "DESCENDING"; break;
            }
             if (drone.is_base_station) std::cout << " (BASE)";
            std::cout << std::endl;
        }
    }

    void logCSIData() {
         if (!csi_log_file.is_open()) return;

        for (const auto& source : drones) {
             if (source.state == Drone::GROUNDED) continue;
             auto it = connectivityMap.find(source.id);
             if (it != connectivityMap.end()) {
                 for (int dest_id : it->second) {
                     const Drone* dest = nullptr;
                     for(const auto& d : drones) {
                         if (d.id == dest_id) { dest = &d; break; }
                     }
                     if (dest && dest->state != Drone::GROUNDED) {
                         double dist = source.distanceTo(*dest);
                         double path_loss = calculatePathLoss(dist);
                         double received_power_dBm = transmit_power_dBm - path_loss;
                         double snr_dB = received_power_dBm - noise_floor_dBm;

                         csi_log_file << simulationTime << ","
                                      << source.id << ","
                                      << dest_id << ","
                                      << dist << ","
                                      << snr_dB << ","
                                      << path_loss << ","
                                      << source.k_factor << ","
                                      << source.path_loss_exp << "\n";
                     }
                 }
             }
        }
        csi_log_file.flush();
    }

    void manageDroneLifecycle(double timeStep) {
         const double LOW_BATTERY_THRESHOLD = 10.0;
        const double CRITICAL_BATTERY_THRESHOLD = 2.0;
        const double ASCEND_DESCEND_RATE = 5.0;

        for (auto& drone : drones) {
            if (drone.is_base_station) continue;

            switch (drone.state) {
                case Drone::ACTIVE:
                    if (drone.battery_level <= CRITICAL_BATTERY_THRESHOLD) {
                        drone.state = Drone::GROUNDED;
                        std::cout << "Drone " << drone.id << " grounded due to critical battery." << std::endl;
                    } else if (drone.battery_level <= LOW_BATTERY_THRESHOLD) {
                        drone.state = Drone::DESCENDING;
                        drone.target_z = worldMinHeight - 1.0;
                         std::cout << "Drone " << drone.id << " descending due to low battery." << std::endl;
                    }
                    break;
                case Drone::DESCENDING:
                    drone.z -= ASCEND_DESCEND_RATE * timeStep;
                    if (drone.z <= worldMinHeight) {
                        drone.z = worldMinHeight;
                        drone.state = Drone::LANDED;
                        std::cout << "Drone " << drone.id << " landed." << std::endl;
                    }
                     if (drone.battery_level <= CRITICAL_BATTERY_THRESHOLD) {
                        drone.state = Drone::GROUNDED;
                        std::cout << "Drone " << drone.id << " grounded while descending." << std::endl;
                    }
                    break;
                case Drone::LANDED:
                     if (drone.battery_level <= CRITICAL_BATTERY_THRESHOLD) {
                        drone.state = Drone::GROUNDED;
                         std::cout << "Drone " << drone.id << " grounded while landed." << std::endl;
                    }
                    break;
                case Drone::ASCENDING:
                    break;
                case Drone::GROUNDED:
                    break;
            }
        }
    }

    double calculatePowerConsumption(const Drone& drone, double timeStep) {
         double power = 0.0;

        if (drone.state != Drone::GROUNDED && drone.state != Drone::LANDED) {
             const double HOVER_POWER = 20.0;
             const double MOVE_POWER_FACTOR = 5.0;

             if (drone.state == Drone::DESCENDING) {
                 power = HOVER_POWER * 0.5;
             } else {
                 power = HOVER_POWER;
             }

            const double BASE_COMM_POWER = 0.5;
            power += BASE_COMM_POWER;
        }

        return power;
    }

    void updateBattery(Drone& drone, double power, double timeStep) {
         if (drone.state == Drone::GROUNDED || drone.state == Drone::LANDED || drone.is_base_station) {
            return;
        }

        double voltage = 11.1;
        double energy_consumed_Ws = power * timeStep;
        double energy_consumed_Wh = energy_consumed_Ws / 3600.0;
        double energy_consumed_mAh = (energy_consumed_Wh / voltage) * 1000.0;

        double percentage_consumed = (energy_consumed_mAh / drone.battery_capacity) * 100.0;
        drone.battery_level -= percentage_consumed;

        if (drone.battery_level < 0) {
            drone.battery_level = 0;
        }
    }

    void initializeRR(int source_node_id) {
        std::cout << "Initializing Reps-Ramalingam from source: " << source_node_id << std::endl;
        rr_source_id = source_node_id;
        dist.clear();
        pred.clear();
        std::priority_queue<PqElement, std::vector<PqElement>, std::greater<PqElement>> empty_pq;
        pq.swap(empty_pq);

        for (const auto& drone : drones) {
            dist[drone.id] = std::numeric_limits<double>::infinity();
            pred[drone.id] = -1;
        }

        if (dist.count(rr_source_id)) {
            dist[rr_source_id] = 0.0;
            pq.push({0.0, rr_source_id});
            processUpdates();
             std::cout << "RR Initialized. Source distance: " << dist[rr_source_id] << std::endl;
        } else {
             std::cerr << "Error: RR source node ID " << rr_source_id << " not found in drones list." << std::endl;
        }
    }

    void processUpdates() {
        while (!pq.empty()) {
            double d = pq.top().first;
            int u = pq.top().second;
            pq.pop();

            if (d > dist[u]) {
                continue;
            }

            if (graph_weights.count(u)) {
                for (const auto& edge : graph_weights[u]) {
                    int v = edge.first;
                    double weight = edge.second;
                    if (dist[u] != std::numeric_limits<double>::infinity() && dist[v] > dist[u] + weight) {
                        dist[v] = dist[u] + weight;
                        pred[v] = u;
                        pq.push({dist[v], v});
                    }
                }
            }
        }
    }

    bool updateEdgeDecrease(int u, int v, double new_weight) {
        graph_weights[u][v] = new_weight;
        bool updated = false;
        if (dist.count(u) && dist.count(v) && dist[u] != std::numeric_limits<double>::infinity() && dist[v] > dist[u] + new_weight) {
            dist[v] = dist[u] + new_weight;
            pred[v] = u;
            pq.push({dist[v], v});
            updated = true;
        }
        return updated;
    }

    bool updateEdgeIncrease(int u, int v) {
         bool updated = false;
         if (graph_weights.count(u)) graph_weights[u].erase(v);

         if (pred.count(v) && pred[v] == u) {
             double old_dist_v = dist[v];
             dist[v] = std::numeric_limits<double>::infinity();
             pred[v] = -1;

             for(const auto& potential_pred_pair : graph_weights) {
                 int potential_pred_node = potential_pred_pair.first;
                 if (graph_weights[potential_pred_node].count(v)) {
                      double weight = graph_weights[potential_pred_node][v];
                      if (dist.count(potential_pred_node) && dist[potential_pred_node] != std::numeric_limits<double>::infinity()) {
                           if (dist[potential_pred_node] + weight < dist[v]) {
                                dist[v] = dist[potential_pred_node] + weight;
                                pred[v] = potential_pred_node;
                           }
                      }
                 }
             }

             if (dist[v] > old_dist_v || dist[v] == std::numeric_limits<double>::infinity()) {
                 pq.push({dist[v], v});
                 updated = true;
             } else if (dist[v] < old_dist_v) {
                 pq.push({dist[v], v});
                 updated = true;
             }
         }
         return updated;
    }

    std::vector<int> getPathRR(int destination_id) {
        std::vector<int> path;
        if (!dist.count(destination_id) || dist[destination_id] == std::numeric_limits<double>::infinity()) {
            return path;
        }

        int current = destination_id;
        while (current != -1 && current != rr_source_id) {
            path.push_back(current);
            if (!pred.count(current)) {
                 std::cerr << "Error: Predecessor not found for node " << current << " in RR path reconstruction." << std::endl;
                 return {};
            }
            current = pred[current];
             if (path.size() > drones.size()) {
                 std::cerr << "Error: Loop detected during RR path reconstruction." << std::endl;
                 return {};
             }
        }

        if (current == rr_source_id) {
            path.push_back(rr_source_id);
            std::reverse(path.begin(), path.end());
        } else {
            path.clear();
        }
        return path;
    }

    std::vector<int> findRouteRR(int source_id, int destination_id) {
         auto start_time = std::chrono::high_resolution_clock::now();

         std::vector<int> path;
         if (source_id != rr_source_id) {
             std::cerr << "Warning: findRouteRR called with source (" << source_id
                       << ") different from RR source (" << rr_source_id
                       << "). RR only computes paths from its source. Returning empty path." << std::endl;
             return path;
         }

         path = getPathRR(destination_id);

         auto end_time = std::chrono::high_resolution_clock::now();
         std::chrono::duration<double, std::milli> duration = end_time - start_time;
         routing_metrics.total_astar_execution_time += duration.count() / 1000.0;
         routing_metrics.total_astar_executions++;

         if (!path.empty()) {
         } else {
         }

         return path;
    }

    void initiateRouteDiscovery(int source_id, int destination_id) {
         std::cout << "Initiating AODV route discovery from " << source_id << " to " << destination_id << std::endl;

        sequence_numbers[source_id]++;

        static int rreq_id_counter = 0;
        int rreq_id = rreq_id_counter++;

        int dest_seq = 0;
        for (const auto& node_routes : route_tables) {
            if (node_routes.second.count(destination_id)) {
                 const auto& route = node_routes.second.at(destination_id);
                 if (route.valid) {
                     dest_seq = std::max(dest_seq, route.sequence_number);
                 }
            }
        }

        AODVRouteRequest rreq(rreq_id, source_id, destination_id,
                             sequence_numbers[source_id], dest_seq,
                             0, simulationTime);

        broadcastRREQ(rreq, source_id);

        routing_metrics.total_route_discoveries++;
    }

    void broadcastRREQ(const AODVRouteRequest& rreq, int broadcasting_node_id) {
         auto it = connectivityMap.find(broadcasting_node_id);
        if (it == connectivityMap.end() || it->second.empty()) {
             std::cout << "Node " << broadcasting_node_id << " has no neighbors to broadcast RREQ " << rreq.rreq_id << std::endl;
            return;
        }

        AODVRouteRequest forwarded_rreq = rreq;
        forwarded_rreq.hop_count++;
        forwarded_rreq.timestamp = simulationTime;

        if (forwarded_rreq.hop_count > AODV_NET_DIAMETER) {
             std::cout << "RREQ " << rreq.rreq_id << " TTL exceeded at node " << broadcasting_node_id << std::endl;
             return;
        }

        rreq_seen[broadcasting_node_id][std::make_pair(rreq.source_id, rreq.rreq_id)] = simulationTime;

        for (int neighbor_id : it->second) {
             if (!rreq_seen[neighbor_id].count(std::make_pair(rreq.source_id, rreq.rreq_id))) {
                 pending_route_requests.push_back(forwarded_rreq);
                 rreq_seen[neighbor_id][std::make_pair(rreq.source_id, rreq.rreq_id)] = simulationTime;
                 std::cout << "Node " << broadcasting_node_id << " forwarding RREQ " << rreq.rreq_id
                           << " to neighbor " << neighbor_id << std::endl;
             } else {
                  std::cout << "Node " << broadcasting_node_id << " skipping forward RREQ " << rreq.rreq_id
                           << " to neighbor " << neighbor_id << " (already seen)" << std::endl;
             }
        }

        routing_metrics.total_rreq_sent++;

        std::cout << "Node " << broadcasting_node_id << " broadcast RREQ " << rreq.rreq_id
                  << " for destination " << rreq.destination_id << " completed." << std::endl;
    }

    void processRREQ(const AODVRouteRequest& rreq, int processing_node_id, int sender_node_id) {
         std::cout << "Node " << processing_node_id << " processing RREQ from " << rreq.source_id
                  << " to " << rreq.destination_id << " (ID: " << rreq.rreq_id
                  << ", hops: " << rreq.hop_count << ", received from: " << sender_node_id << ")" << std::endl;

        if (!rreq_seen[processing_node_id].count(std::make_pair(rreq.source_id, rreq.rreq_id))) {
             std::cout << "Error: Node " << processing_node_id << " processing RREQ it hasn't seen?" << std::endl;
             rreq_seen[processing_node_id][std::make_pair(rreq.source_id, rreq.rreq_id)] = simulationTime;
        }

        updateReverseRoute(processing_node_id, sender_node_id, rreq);

         sequence_numbers[processing_node_id] = std::max(sequence_numbers[processing_node_id], rreq.source_sequence);

        if (processing_node_id == rreq.destination_id) {
            std::cout << "Node " << processing_node_id << " is the destination." << std::endl;
            if (rreq.destination_sequence > sequence_numbers[processing_node_id]) {
                sequence_numbers[processing_node_id] = rreq.destination_sequence;
            }
            generateRREP(rreq, processing_node_id);
        } else {
            bool has_fresh_route = false;
            if (route_tables[processing_node_id].count(rreq.destination_id)) {
                auto& route_entry = route_tables[processing_node_id][rreq.destination_id];
                if (route_entry.valid && route_entry.sequence_number >= rreq.destination_sequence) {
                    has_fresh_route = true;
                }
            }

            if (has_fresh_route) {
                 std::cout << "Node " << processing_node_id << " has fresh AODV route to " << rreq.destination_id << "." << std::endl;
                 generateRREP(rreq, processing_node_id);
            } else {
                 std::cout << "Node " << processing_node_id << " has no fresh route, rebroadcasting RREQ." << std::endl;
                 broadcastRREQ(rreq, processing_node_id);
            }
        }
    }

    void updateReverseRoute(int node_id, int prev_hop_id, const AODVRouteRequest& rreq) {
         auto& route_table = route_tables[node_id];
        bool route_exists = route_table.count(rreq.source_id);
        AODVRouteEntry* existing_route = route_exists ? &route_table[rreq.source_id] : nullptr;

        bool should_update = false;
        if (!route_exists) {
            should_update = true;
             std::cout << "  Reverse route: No existing route to " << rreq.source_id << " from " << node_id << std::endl;
        } else if (rreq.source_sequence > existing_route->sequence_number) {
            should_update = true;
             std::cout << "  Reverse route: New sequence number " << rreq.source_sequence << " > old " << existing_route->sequence_number << std::endl;
        } else if (rreq.source_sequence == existing_route->sequence_number && rreq.hop_count < existing_route->hop_count) {
            should_update = true;
             std::cout << "  Reverse route: Same sequence number " << rreq.source_sequence << ", new hop count " << rreq.hop_count << " < old " << existing_route->hop_count << std::endl;
        } else if (!existing_route->valid) {
             should_update = true;
             std::cout << "  Reverse route: Existing route was invalid." << std::endl;
        }

        if (should_update) {
            route_table[rreq.source_id] = AODVRouteEntry(
                rreq.source_id,
                prev_hop_id,
                rreq.hop_count,
                rreq.source_sequence,
                simulationTime + AODV_ROUTE_LIFETIME,
                true
            );

            std::cout << "Node " << node_id << " updated reverse AODV route to "
                      << rreq.source_id << " via " << prev_hop_id
                      << " (Hops: " << rreq.hop_count << ", Seq: " << rreq.source_sequence << ")" << std::endl;
        } else {
             if (route_exists && existing_route->next_hop_id == prev_hop_id) {
                  existing_route->lifetime = std::max(existing_route->lifetime, simulationTime + AODV_ROUTE_LIFETIME);
             }
        }
    }

    void generateRREP(const AODVRouteRequest& rreq, int generating_node_id) {
         std::cout << "Node " << generating_node_id << " generating RREP for RREQ " << rreq.rreq_id
                  << " (Source: " << rreq.source_id << ", Dest: " << rreq.destination_id << ")" << std::endl;

        int dest_seq_num;
        int hop_count_to_dest;

        if (generating_node_id == rreq.destination_id) {
            sequence_numbers[generating_node_id] = std::max(sequence_numbers[generating_node_id], rreq.source_sequence);
             sequence_numbers[generating_node_id]++;

            dest_seq_num = sequence_numbers[generating_node_id];
            hop_count_to_dest = 0;
             std::cout << "  Generating RREP as destination. Seq: " << dest_seq_num << std::endl;
        } else {
            if (!route_tables[generating_node_id].count(rreq.destination_id) || !route_tables[generating_node_id][rreq.destination_id].valid) {
                std::cerr << "Error: Node " << generating_node_id << " trying to generate RREP without a valid route to " << rreq.destination_id << std::endl;
                return;
            }
            const auto& route_entry = route_tables[generating_node_id][rreq.destination_id];
            dest_seq_num = route_entry.sequence_number;
            hop_count_to_dest = route_entry.hop_count;
             std::cout << "  Generating RREP as intermediate. Seq: " << dest_seq_num << ", Hops to Dest: " << hop_count_to_dest << std::endl;
        }

        AODVRouteReply rrep(
            rreq.source_id,
            rreq.destination_id,
            dest_seq_num,
            hop_count_to_dest,
            AODV_ROUTE_LIFETIME,
            simulationTime
        );

        if (!route_tables[generating_node_id].count(rreq.source_id) || !route_tables[generating_node_id][rreq.source_id].valid) {
            std::cerr << "Error: Node " << generating_node_id << " has no reverse route to RREQ source " << rreq.source_id << " to send RREP." << std::endl;
            return;
        }
        int next_hop_to_source = route_tables[generating_node_id][rreq.source_id].next_hop_id;

        sendRREP(rrep, generating_node_id, next_hop_to_source);
    }

    void sendRREP(const AODVRouteReply& rrep, int sending_node_id, int next_hop_id) {
         auto it = connectivityMap.find(sending_node_id);
        if (it == connectivityMap.end() ||
            std::find(it->second.begin(), it->second.end(), next_hop_id) == it->second.end()) {
            std::cout << "Cannot send RREP from " << sending_node_id << " to " << next_hop_id
                      << ": not a current neighbor." << std::endl;
            return;
        }

        pending_route_replies.push_back(rrep);

        routing_metrics.total_rrep_sent++;

        std::cout << "Node " << sending_node_id << " sending RREP (for Dest " << rrep.destination_id << ") to next hop " << next_hop_id << std::endl;
    }

    void processRREP(const AODVRouteReply& rrep, int processing_node_id, int sender_node_id) {
         std::cout << "Node " << processing_node_id << " processing RREP from " << sender_node_id
                  << " (Source: " << rrep.source_id << ", Dest: " << rrep.destination_id
                  << ", DestSeq: " << rrep.destination_sequence << ", HopsToDest: " << rrep.hop_count << ")" << std::endl;

        updateForwardRoute(processing_node_id, sender_node_id, rrep);

        if (processing_node_id == rrep.source_id) {
            std::cout << "RREP reached original source " << processing_node_id << ". AODV route to "
                      << rrep.destination_id << " established/updated." << std::endl;
            return;
        }

        if (route_tables[processing_node_id].count(rrep.source_id) && route_tables[processing_node_id][rrep.source_id].valid) {
            int next_hop_to_source = route_tables[processing_node_id][rrep.source_id].next_hop_id;

            AODVRouteReply forwarded_rrep = rrep;
            forwarded_rrep.hop_count++;
            forwarded_rrep.timestamp = simulationTime;

            std::cout << "Node " << processing_node_id << " forwarding RREP towards source " << rrep.source_id << " via " << next_hop_to_source << std::endl;
            sendRREP(forwarded_rrep, processing_node_id, next_hop_to_source);
        } else {
            std::cout << "Node " << processing_node_id << " cannot forward RREP: no valid AODV route to source "
                      << rrep.source_id << std::endl;
        }
    }

    void updateForwardRoute(int node_id, int prev_hop_id, const AODVRouteReply& rrep) {
         auto& route_table = route_tables[node_id];
        bool route_exists = route_table.count(rrep.destination_id);
        AODVRouteEntry* existing_route = route_exists ? &route_table[rrep.destination_id] : nullptr;

        int new_hop_count = rrep.hop_count + 1;

        bool should_update = false;
        if (!route_exists) {
            should_update = true;
             std::cout << "  Forward route: No existing route to " << rrep.destination_id << " from " << node_id << std::endl;
        } else if (!existing_route->valid) {
             should_update = true;
             std::cout << "  Forward route: Existing route to " << rrep.destination_id << " was invalid." << std::endl;
        } else if (rrep.destination_sequence > existing_route->sequence_number) {
            should_update = true;
             std::cout << "  Forward route: New sequence number " << rrep.destination_sequence << " > old " << existing_route->sequence_number << std::endl;
        } else if (rrep.destination_sequence == existing_route->sequence_number && new_hop_count < existing_route->hop_count) {
            should_update = true;
             std::cout << "  Forward route: Same sequence number " << rrep.destination_sequence << ", new hop count " << new_hop_count << " < old " << existing_route->hop_count << std::endl;
        }

        if (should_update) {
            route_table[rrep.destination_id] = AODVRouteEntry(
                rrep.destination_id,
                prev_hop_id,
                new_hop_count,
                rrep.destination_sequence,
                simulationTime + rrep.lifetime,
                true
            );

            std::cout << "Node " << node_id << " updated forward AODV route to "
                      << rrep.destination_id << " via " << prev_hop_id
                      << " (Hops: " << new_hop_count << ", Seq: " << rrep.destination_sequence << ")" << std::endl;
        } else {
             if (route_exists && existing_route->valid && existing_route->next_hop_id == prev_hop_id) {
                  existing_route->lifetime = std::max(existing_route->lifetime, simulationTime + rrep.lifetime);
             }
        }
    }

    void generateRERR(int node_id, int broken_link_next_hop_id) {
         std::cout << "Node " << node_id << " detected broken link towards next hop " << broken_link_next_hop_id << std::endl;
        std::vector<std::pair<int, int>> affected_dest_seq_pairs;

        auto& route_table = route_tables[node_id];
        std::vector<int> destinations_to_invalidate;

        for (auto const& [dest_id, route_entry] : route_table) {
            if (route_entry.valid && route_entry.next_hop_id == broken_link_next_hop_id) {
                destinations_to_invalidate.push_back(dest_id);
                affected_dest_seq_pairs.push_back({dest_id, route_entry.sequence_number});
                 std::cout << "  Route to Dest " << dest_id << " via " << broken_link_next_hop_id << " affected." << std::endl;
            }
        }

        for (int dest_id : destinations_to_invalidate) {
             route_table[dest_id].valid = false;
             route_table[dest_id].lifetime = simulationTime;
        }

        if (affected_dest_seq_pairs.empty()) {
             std::cout << "  No active routes affected by broken link to " << broken_link_next_hop_id << std::endl;
            return;
        }

        for (const auto& pair : affected_dest_seq_pairs) {
            int dest_id = pair.first;
            int dest_seq = pair.second;

            AODVRouteError rerr(
                node_id,
                dest_id,
                dest_seq,
                simulationTime
            );

            broadcastRERR(rerr, node_id);

            routing_metrics.total_route_failures++;
        }
    }

    void broadcastRERR(const AODVRouteError& rerr, int broadcasting_node_id) {
         auto it = connectivityMap.find(broadcasting_node_id);
        if (it == connectivityMap.end() || it->second.empty()) {
             std::cout << "Node " << broadcasting_node_id << " has no neighbors to broadcast RERR for Dest " << rerr.destination_id << std::endl;
            return;
        }

        for (int neighbor_id : it->second) {
             bool neighbor_uses_broadcaster = false;
             if (route_tables.count(neighbor_id) && route_tables[neighbor_id].count(rerr.destination_id)) {
                 if (route_tables[neighbor_id][rerr.destination_id].valid &&
                     route_tables[neighbor_id][rerr.destination_id].next_hop_id == broadcasting_node_id) {
                     neighbor_uses_broadcaster = true;
                 }
             }

             if (neighbor_uses_broadcaster) {
                 pending_route_errors.push_back(rerr);
                 std::cout << "Node " << broadcasting_node_id << " sending RERR (for Dest " << rerr.destination_id << ") to neighbor " << neighbor_id << std::endl;
             } else {
                  std::cout << "Node " << broadcasting_node_id << " skipping RERR send to neighbor " << neighbor_id << " (doesn't use this path)" << std::endl;
             }
        }

        routing_metrics.total_rerr_sent++;

        std::cout << "Node " << broadcasting_node_id << " broadcast RERR for destination "
                  << rerr.destination_id << " completed." << std::endl;
    }

    void processRERR(const AODVRouteError& rerr, int processing_node_id, int sender_node_id) {
         std::cout << "Node " << processing_node_id << " processing RERR from " << sender_node_id
                  << " about destination " << rerr.destination_id << std::endl;

        bool route_invalidated = false;
        auto& route_table = route_tables[processing_node_id];

        if (route_table.count(rerr.destination_id)) {
            auto& route_entry = route_table[rerr.destination_id];

            if (route_entry.valid && route_entry.next_hop_id == sender_node_id) {
                route_entry.valid = false;
                route_entry.lifetime = simulationTime;

                route_invalidated = true;
                std::cout << "  Node " << processing_node_id << " invalidated route to "
                          << rerr.destination_id << " (was via " << sender_node_id << ")" << std::endl;
            }
        }

        if (route_invalidated) {
            broadcastRERR(rerr, processing_node_id);
        } else {
             std::cout << "  Node " << processing_node_id << " RERR ignored (no matching active route via " << sender_node_id << ")" << std::endl;
        }
    }

    void processAODVMessages() {
        std::vector<std::pair<AODVRouteRequest, int>> rreqs_to_process;
        std::vector<std::pair<AODVRouteReply, int>> rreps_to_process;
        std::vector<std::pair<AODVRouteError, int>> rerrs_to_process;

        std::map<int, int> rreq_senders;
        std::map<int, int> rrep_senders;
        std::map<int, int> rerr_senders;

        std::vector<AODVRouteRequest> current_rreqs = pending_route_requests;
        pending_route_requests.clear();
        if (!current_rreqs.empty()) std::cout << "\n--- Processing " << current_rreqs.size() << " Pending RREQs ---" << std::endl;
        for (const auto& rreq : current_rreqs) {
             for (auto const& [node_id, seen_map] : rreq_seen) {
                  if (seen_map.count({rreq.source_id, rreq.rreq_id})) {
                       int potential_sender = -1;
                       if (connectivityMap.count(node_id)) {
                            for(int neighbor : connectivityMap.at(node_id)) {
                                 potential_sender = neighbor;
                                 break;
                            }
                       }
                       if (potential_sender != -1) {
                            processRREQ(rreq, node_id, potential_sender);
                       } else if (node_id == rreq.source_id) {
                       } else {
                            std::cout << "Warning: Cannot determine sender for RREQ processing at node " << node_id << std::endl;
                       }
                  }
             }
        }

        std::vector<AODVRouteReply> current_rreps = pending_route_replies;
        pending_route_replies.clear();
         if (!current_rreps.empty()) std::cout << "\n--- Processing " << current_rreps.size() << " Pending RREPs ---" << std::endl;
        for (const auto& rrep : current_rreps) {
             for (auto const& [node_id, route_table_map] : route_tables) {
                  if (route_table_map.count(rrep.source_id)) {
                       const auto& route_to_source = route_table_map.at(rrep.source_id);
                       int potential_sender = -1;
                        if (connectivityMap.count(node_id)) {
                            for(int neighbor : connectivityMap.at(node_id)) {
                                 potential_sender = neighbor;
                                 break;
                            }
                       }
                       if (potential_sender != -1) {
                            processRREP(rrep, node_id, potential_sender);
                       } else {
                            std::cout << "Warning: Cannot determine sender for RREP processing at node " << node_id << std::endl;
                       }
                  }
             }
        }

        std::vector<AODVRouteError> current_rerrs = pending_route_errors;
        pending_route_errors.clear();
         if (!current_rerrs.empty()) std::cout << "\n--- Processing " << current_rerrs.size() << " Pending RERRs ---" << std::endl;
        for (const auto& rerr : current_rerrs) {
             for (auto const& [node_id, neighbors] : connectivityMap) {
                  int potential_sender = -1;
                  if (std::find(neighbors.begin(), neighbors.end(), rerr.source_id) != neighbors.end()) {
                       potential_sender = rerr.source_id;
                  }

                  if (potential_sender != -1) {
                       processRERR(rerr, node_id, potential_sender);
                  } else {
                  }
             }
        }
    }

    std::vector<int> findRouteHybrid(int source_id, int destination_id) {
        std::cout << "\n=== Finding Hybrid RR-AODV Route ===" << std::endl;
        std::cout << "Source: " << source_id << ", Destination: " << destination_id << std::endl;

        // Special case for base stations - direct connection
        if ((source_id == transmitter_base_id && destination_id == receiver_base_id) ||
            (source_id == receiver_base_id && destination_id == transmitter_base_id)) {
            std::cout << "Direct base station connection detected." << std::endl;
            std::vector<int> direct_path = {source_id, destination_id};
            std::cout << "Using direct base station path: " << source_id << " -> " << destination_id << std::endl;
            updateRoutingTablesWithPath(source_id, destination_id, direct_path);
            return direct_path;
        }

        std::vector<int> rr_path;
        if (source_id == rr_source_id) {
            std::cout << "Checking RR path..." << std::endl;
            rr_path = findRouteRR(source_id, destination_id);
            if (!rr_path.empty()) {
                std::cout << "Found RR path (Length: " << rr_path.size() -1 << " hops): ";
                 for (size_t i = 0; i < rr_path.size(); ++i) { std::cout << rr_path[i] << (i == rr_path.size() - 1 ? "" : " -> "); } std::cout << std::endl;
            } else {
                std::cout << "No path found via RR." << std::endl;
            }
        } else {
             std::cout << "Source is not RR source, skipping RR check." << std::endl;
        }

        std::vector<int> aodv_path;
        std::cout << "Checking AODV table..." << std::endl;
        if (route_tables.count(source_id) && route_tables[source_id].count(destination_id)) {
            auto& route_entry = route_tables[source_id][destination_id];
            if (route_entry.valid && route_entry.lifetime > simulationTime) {
                int current = source_id;
                aodv_path.push_back(current);
                int hops = 0;
                while (current != destination_id && hops < AODV_NET_DIAMETER) {
                     if (!route_tables.count(current) || !route_tables[current].count(destination_id) || !route_tables[current][destination_id].valid) {
                         std::cout << "  AODV path broken at node " << current << std::endl;
                         aodv_path.clear();
                         break;
                     }
                     int next_hop = route_tables[current][destination_id].next_hop_id;
                     bool loop_detected = false;
                     for(int node_in_path : aodv_path) { if (node_in_path == next_hop) { loop_detected = true; break; } }
                     if (loop_detected) {
                          std::cout << "  AODV loop detected (" << current << " -> " << next_hop << ")" << std::endl;
                          aodv_path.clear();
                          break;
                     }

                     current = next_hop;
                     aodv_path.push_back(current);
                     hops++;
                }

                 if (current != destination_id && !aodv_path.empty()) {
                      std::cout << "  AODV path reconstruction failed (hops=" << hops << ", current=" << current << ")" << std::endl;
                      aodv_path.clear();
                 }

                if (!aodv_path.empty()) {
                    std::cout << "Found valid AODV route (Length: " << aodv_path.size() -1 << " hops): ";
                     for (size_t i = 0; i < aodv_path.size(); ++i) { std::cout << aodv_path[i] << (i == aodv_path.size() - 1 ? "" : " -> "); } std::cout << std::endl;
                }
            } else {
                 std::cout << "AODV route exists but is invalid or expired." << std::endl;
            }
        } else {
             std::cout << "No AODV route entry found." << std::endl;
        }

        if (!rr_path.empty()) {
            std::cout << "Using RR path." << std::endl;
            updateRoutingTablesWithPath(source_id, destination_id, rr_path);
            return rr_path;
        }

        if (!aodv_path.empty()) {
            std::cout << "Using existing AODV path." << std::endl;
            return aodv_path;
        }

        // Check direct connectivity
        auto it = connectivityMap.find(source_id);
        if (it != connectivityMap.end()) {
            if (std::find(it->second.begin(), it->second.end(), destination_id) != it->second.end()) {
                std::cout << "Direct connection found between " << source_id << " and " << destination_id << std::endl;
                std::vector<int> direct_path = {source_id, destination_id};
                updateRoutingTablesWithPath(source_id, destination_id, direct_path);
                return direct_path;
            }
        }

        std::cout << "No existing routes found (RR or AODV), initiating AODV route discovery..." << std::endl;
        initiateRouteDiscovery(source_id, destination_id);
        processAODVMessages();

         std::cout << "Checking AODV table again after discovery attempt..." << std::endl;
         if (route_tables.count(source_id) && route_tables[source_id].count(destination_id)) {
             auto& route_entry = route_tables[source_id][destination_id];
             if (route_entry.valid && route_entry.lifetime > simulationTime) {
                 int current = source_id;
                 aodv_path.push_back(current);
                 int hops = 0;
                 while (current != destination_id && hops < AODV_NET_DIAMETER) {
                      if (!route_tables.count(current) || !route_tables[current].count(destination_id) || !route_tables[current][destination_id].valid) { aodv_path.clear(); break; }
                      int next_hop = route_tables[current][destination_id].next_hop_id;
                      bool loop_detected = false;
                      for(int node_in_path : aodv_path) { if (node_in_path == next_hop) { loop_detected = true; break; } }
                      if (loop_detected) { aodv_path.clear(); break; }
                      current = next_hop;
                      aodv_path.push_back(current);
                      hops++;
                 }
                  if (current != destination_id && !aodv_path.empty()) { aodv_path.clear(); }

                 if (!aodv_path.empty()) {
                     std::cout << "AODV route discovery successful, found path (Length: " << aodv_path.size() -1 << " hops): ";
                      for (size_t i = 0; i < aodv_path.size(); ++i) { std::cout << aodv_path[i] << (i == aodv_path.size() - 1 ? "" : " -> "); } std::cout << std::endl;
                     return aodv_path;
                 }
             }
         }

        std::cout << "No route found using RR or AODV (including discovery attempt)." << std::endl;
        return std::vector<int>();
    }

    void updateRoutingTablesWithPath(int source_id, int destination_id, const std::vector<int>& path) {
         if (path.size() < 2) return;

        std::cout << "Updating AODV routing tables with path (Length: " << path.size() - 1 << " hops)" << std::endl;

        int dest_seq = sequence_numbers.count(destination_id) ? sequence_numbers[destination_id] : 0;
        int src_seq = sequence_numbers.count(source_id) ? sequence_numbers[source_id] : 0;

        for (size_t i = 0; i < path.size() - 1; ++i) {
            int current_node = path[i];
            int next_hop = path[i + 1];
            int hops_to_dest = path.size() - 1 - i;

            route_tables[current_node][destination_id] = AODVRouteEntry(
                destination_id,
                next_hop,
                hops_to_dest,
                dest_seq,
                simulationTime + AODV_ROUTE_LIFETIME,
                true
            );
        }

        for (size_t i = path.size() - 1; i > 0; --i) {
            int current_node = path[i];
            int next_hop = path[i - 1];
            int hops_to_src = i;

            route_tables[current_node][source_id] = AODVRouteEntry(
                source_id,
                next_hop,
                hops_to_src,
                src_seq,
                simulationTime + AODV_ROUTE_LIFETIME,
                true
            );
        }
         std::cout << "AODV table update complete for path." << std::endl;
    }

    void createPacket(int source_id, int destination_id) {
         const Drone* source_drone = nullptr;
        const Drone* dest_drone = nullptr;
        for (const auto& drone : drones) {
            if (drone.id == source_id) source_drone = &drone;
            if (drone.id == destination_id) dest_drone = &drone;
        }

        if (!source_drone || !dest_drone) {
            std::cerr << "Error creating packet: Invalid source (" << source_id << ") or destination (" << destination_id << ") ID." << std::endl;
            return;
        }

        std::cout << "Creating packet from Drone " << source_id
                  << " at (" << source_drone->x << ", " << source_drone->y << ", " << source_drone->z << ")"
                  << " to Drone " << destination_id
                  << " at (" << dest_drone->x << ", " << dest_drone->y << ", " << dest_drone->z << ")"
                  << std::endl;

        active_packets.emplace_back(source_id, destination_id, simulationTime);
    }

    void processPackets(double timeStep) {
         std::vector<size_t> completed_packet_indices;

        for (size_t i = 0; i < active_packets.size(); ++i) {
            auto& packet = active_packets[i];

            if (packet.delivered) {
                completed_packet_indices.push_back(i);
                continue;
            }

            Drone* current_drone = nullptr;
            for (auto& drone : drones) {
                if (drone.id == packet.current_node_id) {
                    current_drone = &drone;
                    break;
                }
            }

            if (!current_drone) {
                 std::cout << "Packet Error: Current node " << packet.current_node_id << " not found." << std::endl;
                 completed_packet_indices.push_back(i);
                 continue;
            }

            // Allow base stations to forward packets even when landed
            if ((current_drone->state == Drone::GROUNDED) ||
                (current_drone->state == Drone::LANDED && !current_drone->is_base_station)) {
                 std::cout << "Packet held at inactive drone " << current_drone->id << " ("
                           << (current_drone->state == Drone::GROUNDED ? "Grounded" : "Landed")
                           << (current_drone->is_base_station ? " Base Station" : "") << ")" << std::endl;
                 continue;
            }

            double processing_energy_Ws = PACKET_PROCESSING_POWER * timeStep;
            double voltage = 11.1;
            double processing_energy_mAh = (processing_energy_Ws / 3600.0 / voltage) * 1000.0;
            if (!current_drone->is_base_station) {
                 double percentage_consumed = (processing_energy_mAh / current_drone->battery_capacity) * 100.0;
                 current_drone->battery_level -= percentage_consumed;
                 if (current_drone->battery_level < 0) current_drone->battery_level = 0;
            }

            if (packet.current_node_id == packet.destination_id) {
                packet.delivered = true;
                double latency = simulationTime - packet.creation_time;
                routing_metrics.total_end_to_end_latency += latency;
                routing_metrics.total_packets_delivered++;

                std::cout << "Packet delivered from " << packet.source_id
                          << " to " << packet.destination_id
                          << " (Path hops: " << packet.path.size() -1 << ", Latency: " << latency << "s)" << std::endl;
                std::cout << "  Path: "; for(size_t p=0; p<packet.path.size(); ++p) {std::cout << packet.path[p] << (p==packet.path.size()-1?"":" -> ");} std::cout << std::endl;

                // Log metrics immediately after packet delivery
                try {
                    logMetricsData();
                } catch (const std::exception& e) {
                    std::cerr << "Error logging metrics after packet delivery: " << e.what() << std::endl;
                }

                completed_packet_indices.push_back(i);
                continue;
            }

            std::vector<int> route = findRouteHybrid(packet.current_node_id, packet.destination_id);

            if (route.empty() || route.size() < 2) {
                std::cout << "Packet at node " << packet.current_node_id << ": No route found to " << packet.destination_id << ". Holding packet." << std::endl;
                continue;
            }

            int next_hop_id = route[1];

            Drone* next_hop_drone = nullptr;
             for (auto& drone : drones) { if (drone.id == next_hop_id) { next_hop_drone = &drone; break; } }

            auto conn_it = connectivityMap.find(packet.current_node_id);
            if (!next_hop_drone || next_hop_drone->state == Drone::GROUNDED ||
                conn_it == connectivityMap.end() ||
                std::find(conn_it->second.begin(), conn_it->second.end(), next_hop_id) == conn_it->second.end())
            {
                std::cout << "Packet at node " << packet.current_node_id << ": Next hop " << next_hop_id
                          << " is invalid, inactive, or not currently reachable. Holding packet." << std::endl;
                continue;
            }

            double distance = current_drone->distanceTo(*next_hop_drone);
            double tx_power = calculateTransmissionPower(distance);

            if (!current_drone->is_base_station) {
                 double tx_energy_Ws = tx_power * timeStep;
                 double tx_energy_mAh = (tx_energy_Ws / 3600.0 / voltage) * 1000.0;
                 double tx_percentage = (tx_energy_mAh / current_drone->battery_capacity) * 100.0;
                 current_drone->battery_level -= tx_percentage;
                 if (current_drone->battery_level < 0) current_drone->battery_level = 0;
            }

            std::cout << "Packet forwarded from " << packet.current_node_id
                      << " to " << next_hop_id
                      << " (Route: " << route[0] << "->" << route[1] << ", Dist: " << distance << "m, TxPower: " << tx_power << "W)" << std::endl;

            packet.current_node_id = next_hop_id;
            packet.last_hop_time = simulationTime;
            packet.path.push_back(next_hop_id);
        }

        std::sort(completed_packet_indices.rbegin(), completed_packet_indices.rend());
        for (size_t index : completed_packet_indices) {
            if (index < active_packets.size()) {
                 active_packets.erase(active_packets.begin() + index);
            }
        }
    }

    void printRoutingMetrics() {
         std::cout << "\n=== Routing Metrics (Time: " << simulationTime << ") ===" << std::endl;
        std::cout << "Packets Delivered: " << routing_metrics.total_packets_delivered << std::endl;
        std::cout << "Average End-to-End Latency: " << routing_metrics.getAverageLatency() << " s" << std::endl;
        std::cout << "Total AODV Route Discoveries Initiated: " << routing_metrics.total_route_discoveries << std::endl;
        std::cout << "Total AODV Route Failures (RERRs Generated): " << routing_metrics.total_route_failures << std::endl;
        std::cout << "AODV RREQ Sent: " << routing_metrics.total_rreq_sent << std::endl;
        std::cout << "AODV RREP Sent: " << routing_metrics.total_rrep_sent << std::endl;
        std::cout << "AODV RERR Sent: " << routing_metrics.total_rerr_sent << std::endl;

        if (!active_packets.empty()) {
            std::cout << "Active Packets: " << active_packets.size() << std::endl;
            for (const auto& packet : active_packets) {
                std::cout << "  Packet Src:" << packet.source_id << " Dst:" << packet.destination_id
                          << " Current:" << packet.current_node_id << " Hops:" << packet.path.size() -1
                          << " Age:" << simulationTime - packet.creation_time << "s" << std::endl;
            }
        } else {
             std::cout << "Active Packets: 0" << std::endl;
        }
        std::cout << "======================================" << std::endl;
    }

    void cleanupAODVState() {
         const double MAX_RREQ_SEEN_AGE = AODV_ROUTE_DISCOVERY_TIMEOUT * 2.0;

         for (auto& node_routes_pair : route_tables) {
             auto& node_routes = node_routes_pair.second;
             for (auto it = node_routes.begin(); it != node_routes.end(); /* no increment */) {
                 if (it->second.valid && it->second.lifetime <= simulationTime) {
                     it->second.valid = false;
                      std::cout << "AODV route expired: Node " << node_routes_pair.first << " -> Dest " << it->first << std::endl;
                 }
                 ++it;
             }
         }

         for (auto& node_seen_pair : rreq_seen) {
             auto& seen_map = node_seen_pair.second;
             for (auto it = seen_map.begin(); it != seen_map.end(); /* no increment */) {
                 if (simulationTime - it->second > MAX_RREQ_SEEN_AGE) {
                     it = seen_map.erase(it);
                 } else {
                     ++it;
                 }
             }
         }
    }

    double calculateTransmissionPower(double distance) {
         double base_power = 0.1;

        double distance_factor = 0.0001 * distance * distance;

        double max_power = 1.0;

        return std::min(max_power, base_power + distance_factor);
    }

    double calculatePathLoss(double distance) {
         if (distance <= 0) distance = 1.0;
         double fc_MHz = fc / 1e6;
         double fspl = 20.0 * std::log10(distance) + 20.0 * std::log10(fc_MHz) - 27.55;

         double additional_loss = 2.0;

         return fspl + additional_loss;
    }

    void printConnectivityMap() {
         std::cout << "\n=== CONNECTIVITY MAP DETAILS (Time: " << simulationTime << ") ===" << std::endl;
        for (const auto& drone : drones) {
             std::cout << "Drone " << drone.id << " (State: " << drone.state << ") connected to: ";
             auto it = connectivityMap.find(drone.id);
             if (it == connectivityMap.end() || it->second.empty()) {
                 std::cout << "NONE";
             } else {
                 for (int neighbor : it->second) {
                     const Drone* neighbor_drone = nullptr;
                     for(const auto& d : drones) if(d.id == neighbor) {neighbor_drone = &d; break;}
                     if(neighbor_drone) {
                         double dist = drone.distanceTo(*neighbor_drone);
                         double pl = calculatePathLoss(dist);
                         double snr = transmit_power_dBm - pl - noise_floor_dBm;
                         std::cout << neighbor << " (d=" << dist << ", snr=" << snr << ") ";
                     } else {
                          std::cout << neighbor << " (N/A) ";
                     }
                 }
             }
             std::cout << std::endl;
        }
        std::cout << "==============================================\n";
    }

     void printAODVTables() {
         std::cout << "\n--- AODV Routing Tables (Time: " << simulationTime << ") ---" << std::endl;
         for (const auto& node_routes_pair : route_tables) {
             int node_id = node_routes_pair.first;
             const auto& node_routes = node_routes_pair.second;
             if (node_routes.empty()) continue;

             std::cout << " Node " << node_id << ":" << std::endl;
             for (const auto& route_pair : node_routes) {
                 const auto& route = route_pair.second;
                 std::cout << "   -> Dest: " << route.destination_id
                           << " | NextHop: " << route.next_hop_id
                           << " | Hops: " << route.hop_count
                           << " | Seq#: " << route.sequence_number
                           << " | Lifetime: " << route.lifetime
                           << " | Valid: " << (route.valid ? "YES" : "NO")
                           << std::endl;
             }
         }
         std::cout << "-----------------------------------------" << std::endl;
     }

     void printRRState() {
         std::cout << "\n--- Reps-Ramalingam State (Source: " << rr_source_id << ", Time: " << simulationTime << ") ---" << std::endl;
         for (const auto& drone : drones) {
             int id = drone.id;
             double d_val = dist.count(id) ? dist.at(id) : std::numeric_limits<double>::infinity();
             int p_val = pred.count(id) ? pred.at(id) : -1;
             std::cout << " Node " << id << ": Dist = " << d_val << ", Pred = " << p_val << std::endl;
         }
          std::cout << "----------------------------------------------------" << std::endl;
     }

    void debugState() {
        std::cout << "==== Debug State ====" << std::endl;
        std::cout << "Number of drones: " << drones.size() << std::endl;
        std::cout << "Simulation time: " << simulationTime << std::endl;
        std::cout << "Transmitter base ID: " << transmitter_base_id << std::endl;
        std::cout << "Receiver base ID: " << receiver_base_id << std::endl;
        std::cout << "Connectivity map size: " << connectivityMap.size() << std::endl;
        std::cout << "RR source ID: " << rr_source_id << std::endl;
        std::cout << "CSI log file open: " << (csi_log_file.is_open() ? "YES" : "NO") << std::endl;
        std::cout << "Metrics log file open: " << (metrics_log_file.is_open() ? "YES" : "NO") << std::endl;
        std::cout << "======================" << std::endl;
    }
};

// --- Main Function ---
int main() {
    const int NUM_DRONES = 15;
    const double WORLD_WIDTH = 200.0;
    const double WORLD_HEIGHT = 200.0;
    const double WORLD_MIN_HEIGHT = 10.0;
    const double WORLD_MAX_HEIGHT = 50.0;
    const double COMM_RANGE = 70.0;
    const int SIMULATION_STEPS = 500;
    const double TIME_STEP = 0.5;

    DroneNetworkSimulator simulator(NUM_DRONES, WORLD_WIDTH, WORLD_HEIGHT, COMM_RANGE,
                                   WORLD_MIN_HEIGHT, WORLD_MAX_HEIGHT);
    simulator.runSimulation(SIMULATION_STEPS, TIME_STEP);

    return 0;
}
// --- End of File ---