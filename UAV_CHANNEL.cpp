#include <iostream>
#include <vector>
#include <random>
#include <cmath>
#include <complex>
#include <numeric>
#include <algorithm>

// Optional: If you want to keep plotting functionality
// You would need to include a C++ plotting library like matplotlib-cpp, gnuplot-iostream, etc.
// For this conversion, I'll comment out plotting sections

int main() {
    // Random number generation setup
    std::random_device rd;
    std::mt19937 gen(rd());
    
    // Number of Relayed UAVs
    const int N_Relayed_UAVs = 20;
    
    // Generate random coordinates of Relayed UAVs
    std::uniform_real_distribution<double> x_y_dist(-50.0, 50.0);
    std::uniform_real_distribution<double> z_dist(50.0, 100.0);
    
    std::vector<double> X_UAVs(N_Relayed_UAVs);
    std::vector<double> Y_UAVs(N_Relayed_UAVs);
    std::vector<double> Z_UAVs(N_Relayed_UAVs);
    
    for (int i = 0; i < N_Relayed_UAVs; i++) {
        X_UAVs[i] = x_y_dist(gen);
        Y_UAVs[i] = x_y_dist(gen);
        Z_UAVs[i] = z_dist(gen);
    }
    
    // Arrange the coordinates in x, y, z format
    std::vector<std::vector<double>> xyz(N_Relayed_UAVs, std::vector<double>(3));
    for (int i = 0; i < N_Relayed_UAVs; i++) {
        xyz[i][0] = X_UAVs[i];
        xyz[i][1] = Y_UAVs[i];
        xyz[i][2] = Z_UAVs[i];
    }
    
    // Display the Randomly distributed Relayed UAVs in a 3D space
    // Note: Plotting is removed in this C++ version
    // You would need to use a C++ plotting library if visualization is required
    
    /*
     * Calculate the elevation angle between the transmitter
     * and the Relayed-enabled UAVs
     */
    std::vector<double> d_H(N_Relayed_UAVs);
    std::vector<double> d_V(N_Relayed_UAVs);
    std::vector<double> theta(N_Relayed_UAVs);
    
    for (int i = 0; i < N_Relayed_UAVs; i++) {
        d_H[i] = std::sqrt(std::pow(xyz[i][0], 2) + std::pow(xyz[i][1], 2));
        d_V[i] = xyz[i][2];
        theta[i] = std::atan(d_V[i] / d_H[i]);
    }
    
    // Plot the histogram of the theta
    // Note: Plotting is removed in this C++ version
    
    /*
     * Calculate the LOS probability
     */
    std::vector<double> Prob_LOS(N_Relayed_UAVs);
    const double a1 = 12.08;
    const double b1 = 0.11;
    
    for (int i = 0; i < N_Relayed_UAVs; i++) {
        Prob_LOS[i] = 1.0 / (1.0 + a1 * std::exp(-b1 * (theta[i] - a1)));
    }
    
    /*
     * Calculate the Pathloss exponent
     */
    std::vector<double> Path_Loss_Expo(N_Relayed_UAVs);
    const double alpha_pi_by_2 = 2.0;
    const double alpha_zero = 3.5;
    const double Prob_LOS_pi_by_2 = 1.0 / (1.0 + a1 * std::exp(-b1 * (M_PI / 2.0 - a1)));
    const double Prob_LOS_zero = 1.0 / (1.0 + a1 * std::exp(-b1 * (0.0 - a1)));
    const double a2 = (alpha_pi_by_2 - alpha_zero) / (Prob_LOS_pi_by_2 - Prob_LOS_zero);
    const double b2 = alpha_zero - a2 * Prob_LOS_zero;
    
    for (int i = 0; i < N_Relayed_UAVs; i++) {
        Path_Loss_Expo[i] = a2 * Prob_LOS[i] + b2;
    }
    
    /*
     * Channel Realization
     */
    const int Number_Channel_Realization = 1000; // 10^3
    
    // Define the Doppler parameters
    // Carrier frequency in Hz
    const double fc = 2.4e9;
    // Relative speed between the relayed UAV and the transmitter
    const double v = 20.0;
    // Speed of light in m/s
    const double c = 3.0e8;
    // Symbol duration in seconds
    // This may also be interpreted as a frame duration
    const double Ts = 1.0e-6;
    // Evaluate the Doppler shift factor
    const double f_Doppler = v * fc / c;
    
    std::vector<double> K(N_Relayed_UAVs);
    const double K_zero = 1.0;
    const double K_pi_by_2 = 15.0;
    const double a3 = K_zero;
    const double b3 = (2.0 / M_PI) * std::log(K_pi_by_2 / K_zero);
    
    const double P_LOS_plus_NLOS = 1.0;
    
    std::vector<double> sigma(N_Relayed_UAVs);
    std::vector<double> s(N_Relayed_UAVs);
    
    // Create 2D vectors for channel coefficients
    std::vector<std::vector<double>> h_real(N_Relayed_UAVs, std::vector<double>(Number_Channel_Realization));
    std::vector<std::vector<double>> h_imag(N_Relayed_UAVs, std::vector<double>(Number_Channel_Realization));
    std::vector<std::vector<std::complex<double>>> h(N_Relayed_UAVs, std::vector<std::complex<double>>(Number_Channel_Realization));
    std::vector<std::vector<std::complex<double>>> h_Doppler(N_Relayed_UAVs, std::vector<std::complex<double>>(Number_Channel_Realization));
    std::vector<std::vector<double>> h_abs(N_Relayed_UAVs, std::vector<double>(Number_Channel_Realization));
    std::vector<double> h_mean(N_Relayed_UAVs);
    
    // Normal distribution for random values
    std::normal_distribution<double> normal_dist(0.0, 1.0);
    
    for (int i = 0; i < N_Relayed_UAVs; i++) {
        K[i] = a3 * std::exp(b3 * theta[i]);
        sigma[i] = P_LOS_plus_NLOS / std::sqrt(2.0 * (K[i] + 1.0));
        s[i] = std::sqrt(K[i] / (K[i] + 1.0) * P_LOS_plus_NLOS);
        
        for (int j = 0; j < Number_Channel_Realization; j++) {
            h_real[i][j] = sigma[i] * normal_dist(gen) + s[i];
            h_imag[i][j] = sigma[i] * normal_dist(gen) + 0.0;
            h[i][j] = std::complex<double>(h_real[i][j], h_imag[i][j]);
            
            // Introduce Impact of Doppler Shift
            h_Doppler[i][j] = h[i][j] * std::exp(std::complex<double>(0, -2.0 * M_PI * f_Doppler * Ts));
            h_abs[i][j] = std::abs(h_Doppler[i][j]);
        }
        
        // Calculate mean of h_abs
        h_mean[i] = std::accumulate(h_abs[i].begin(), h_abs[i].end(), 0.0) / Number_Channel_Realization;
        
        // Recalculate h_abs and h_mean without Doppler (as in the Python code)
        for (int j = 0; j < Number_Channel_Realization; j++) {
            h_abs[i][j] = std::abs(h[i][j]);
        }
        h_mean[i] = std::accumulate(h_abs[i].begin(), h_abs[i].end(), 0.0) / Number_Channel_Realization;
    }
    
    // Plot histogram
    // Note: Plotting is removed in this C++ version
    
    /*
     * Calculate the Pathloss
     */
    const double Pt = 1.0;
    std::vector<double> d(N_Relayed_UAVs);
    std::vector<double> Received_Signal_Power(N_Relayed_UAVs);
    
    for (int i = 0; i < N_Relayed_UAVs; i++) {
        d[i] = std::sqrt(std::pow(d_H[i], 2) + std::pow(d_V[i], 2));
        Received_Signal_Power[i] = h_mean[i] * std::pow(d[i], -Path_Loss_Expo[i]) * Pt;
    }
    
    // Print some results to verify
    std::cout << "UAV Coordinates (first 5):" << std::endl;
    for (int i = 0; i < std::min(5, N_Relayed_UAVs); i++) {
        std::cout << "UAV " << i << ": (" << X_UAVs[i] << ", " << Y_UAVs[i] << ", " << Z_UAVs[i] << ")" << std::endl;
    }
    
    std::cout << "\nElevation Angles (first 5):" << std::endl;
    for (int i = 0; i < std::min(5, N_Relayed_UAVs); i++) {
        std::cout << "UAV " << i << ": " << theta[i] * 180.0 / M_PI << " degrees" << std::endl;
    }
    
    std::cout << "\nReceived Signal Power (first 5):" << std::endl;
    for (int i = 0; i < std::min(5, N_Relayed_UAVs); i++) {
        std::cout << "UAV " << i << ": " << Received_Signal_Power[i] << std::endl;
    }
    
    return 0;
}
