/**
 * @file he_distance.cpp
 * @brief Homomorphic Encryption for Euclidean Distance (Actual Distance, not squared)
 *        with piecewise linear approximation for square root.
 *        This linear approximation can be: L(x) = f(a) + f'(a) * (x - a) where f'(x) = 1/(2*sqrt(x))
 *        We use different linear approximations for different ranges of x.
 */

#include <iostream>
#include <vector>
#include <cmath>
#include <chrono> // For timing
#include "seal/seal.h"

using namespace std;
using namespace seal;
using namespace std::chrono;

// Helper function to print vectors
template <typename T>
void print_vector(const vector<T>& vec, const string& label) {
    cout << label << ": [ ";
    size_t print_count = min((size_t)5, vec.size());
    for (size_t i = 0; i < print_count; ++i) {
        cout << vec[i] << (i == print_count - 1 ? "" : ", ");
    }
    if (vec.size() > print_count) {
        cout << "... ";
    }
    cout << "]" << endl;
}

// We'll use a more sophisticated approach for sqrt approximation
// Instead of polynomial coefficients, we'll use a piecewise approximation
// with different parameters for different ranges of squared distances

// For small distances (0-100): sqrt(x) ≈ a1 + b1*x
// Optimized for x ≈ 60 (sqrt(60) ≈ 7.75)
const double small_range_constant = 0.5;
const double small_range_linear = 0.12;

// For medium distances (100-1000): sqrt(x) ≈ a2 + b2*x
// Optimized for x ≈ 875 (sqrt(875) ≈ 29.58)
const double medium_range_constant = 5.0;
const double medium_range_linear = 0.028;

// For large distances (1000-5000): sqrt(x) ≈ a3 + b3*x
// Optimized for x ≈ 4500 (sqrt(4500) ≈ 67.1)
const double large_range_constant = 10.0;
const double large_range_linear = 0.0127;

// For very large distances (5000-10000): sqrt(x) ≈ a4 + b4*x
// Optimized for x ≈ 7625 (sqrt(7625) ≈ 87.3)
const double very_large_range_constant = 18.0;
const double very_large_range_linear = 0.0091;

int main() {
    cout << "--- HE Euclidean Distance Calculation (Actual Distance) ---" << endl;
    auto time_start_total = high_resolution_clock::now();

    // 1. Set up SEAL parameters (CKKS)
    EncryptionParameters params(scheme_type::ckks);

    // Increased polynomial modulus degree to allow for deeper computation
    size_t poly_modulus_degree = 32768;
    params.set_poly_modulus_degree(poly_modulus_degree);

    // Set coefficient modulus
    vector<int> coeff_modulus_bits = {
        60, 40, 40, 40, 40, 40, 40, 40, 40, 40, 60
    };
    params.set_coeff_modulus(CoeffModulus::Create(poly_modulus_degree, coeff_modulus_bits));

    // Create SEALContext
    SEALContext context(params);

    // Set scale for encoding
    double scale = pow(2.0, 40);

    cout << "\nSEAL parameters set up:" << endl;
    cout << "  Poly Modulus Degree: " << poly_modulus_degree << endl;
    cout << "  Coeff Modulus Bits: ";
    for(int bits : coeff_modulus_bits) cout << bits << " ";
    cout << endl;

    // 2. Generate Keys
    auto time_start_keys = high_resolution_clock::now();
    KeyGenerator keygen(context);
    SecretKey secret_key = keygen.secret_key();
    PublicKey public_key;
    keygen.create_public_key(public_key);
    RelinKeys relin_keys;
    keygen.create_relin_keys(relin_keys);
    auto time_end_keys = high_resolution_clock::now();
    cout << "\nKeys generated (" << duration_cast<milliseconds>(time_end_keys - time_start_keys).count() << " ms)." << endl;

    // 3. Create Encryptor, Decryptor, Evaluator, CKKSEncoder
    Encryptor encryptor(context, public_key);
    Evaluator evaluator(context);
    Decryptor decryptor(context, secret_key);
    CKKSEncoder encoder(context);
    size_t slot_count = encoder.slot_count();
    cout << "CKKS encoder slot count: " << slot_count << endl;

    // 4. Define Input Points (Plaintext)
    // You can modify these points to test different distances

    // Choose one of the test cases by uncommenting it:

    // Test case 1: Small distance (expected squared distance < 100)
    // vector<double> p1 = { 1.0, 2.0, 3.0 };
    // vector<double> p2 = { 5.0, 8.0, 1.0 };

    // Test case 2: Medium distance (expected squared distance between 100 and 1000)
    // vector<double> p1 = { 5.0, 18.0, 2.0 };
    // vector<double> p2 = { 20.0, 13.0, -23.0 };

    // Test case 3: Large distance (expected squared distance > 1000)
    // vector<double> p1 = { 5.0, 10.0, 15.0 };
    // vector<double> p2 = { 25.0, 50.0, -35.0 };

    // Test case 4: Very large distance (expected squared distance > 5000)
    vector<double> p1 = { 5.0, 10.0, 25.0 };
    vector<double> p2 = { 25.0, 50.0, -53.0 };

    // Or define your own custom 3D points:
    // vector<double> p1 = { x1, y1, z1 };
    // vector<double> p2 = { x2, y2, z2 };

    cout << "\nInput Points:" << endl;
    print_vector(p1, "  Point P1");
    print_vector(p2, "  Point P2");

    // Calculate expected squared distance and actual distance
    double dx = p2[0] - p1[0]; 
    double dy = p2[1] - p1[1]; 
    double dz = p2[2] - p1[2]; 
    double expected_dist_sq = dx * dx + dy * dy + dz * dz; 
    double expected_dist = sqrt(expected_dist_sq); 

    cout << "Expected Squared Distance: " << expected_dist_sq << endl;
    cout << "Expected Actual Distance:  " << expected_dist << endl;

    // 5. Encode and Encrypt Coordinates
    auto time_start_encrypt = high_resolution_clock::now();
    Plaintext pt_x1, pt_y1, pt_z1, pt_x2, pt_y2, pt_z2;
    Ciphertext enc_x1, enc_y1, enc_z1, enc_x2, enc_y2, enc_z2;

    encoder.encode(p1[0], scale, pt_x1);
    encoder.encode(p1[1], scale, pt_y1);
    encoder.encode(p1[2], scale, pt_z1);
    encoder.encode(p2[0], scale, pt_x2);
    encoder.encode(p2[1], scale, pt_y2);
    encoder.encode(p2[2], scale, pt_z2);

    encryptor.encrypt(pt_x1, enc_x1);
    encryptor.encrypt(pt_y1, enc_y1);
    encryptor.encrypt(pt_z1, enc_z1);
    encryptor.encrypt(pt_x2, enc_x2);
    encryptor.encrypt(pt_y2, enc_y2);
    encryptor.encrypt(pt_z2, enc_z2);

    auto time_end_encrypt = high_resolution_clock::now();
    cout << "\nCoordinates Encrypted (" << duration_cast<milliseconds>(time_end_encrypt - time_start_encrypt).count() << " ms)." << endl;

    // 6. Compute Encrypted Squared Distance (enc_dist_sq)
    auto time_start_sqdist = high_resolution_clock::now();

    // Calculate differences between coordinates
    Ciphertext enc_dx, enc_dy, enc_dz;
    evaluator.sub(enc_x2, enc_x1, enc_dx);
    evaluator.sub(enc_y2, enc_y1, enc_dy);
    evaluator.sub(enc_z2, enc_z1, enc_dz);

    // Square the differences
    Ciphertext enc_dx_sq, enc_dy_sq, enc_dz_sq;
    evaluator.multiply(enc_dx, enc_dx, enc_dx_sq); // dx^2
    evaluator.multiply(enc_dy, enc_dy, enc_dy_sq); // dy^2
    evaluator.multiply(enc_dz, enc_dz, enc_dz_sq); // dz^2

    // Relinearize squares
    evaluator.relinearize_inplace(enc_dx_sq, relin_keys);
    evaluator.relinearize_inplace(enc_dy_sq, relin_keys);
    evaluator.relinearize_inplace(enc_dz_sq, relin_keys);

    // Rescale to manage scale growth
    evaluator.rescale_to_next_inplace(enc_dx_sq);
    evaluator.rescale_to_next_inplace(enc_dy_sq);
    evaluator.rescale_to_next_inplace(enc_dz_sq);

    // Ensure all ciphertexts are at the same level before adding
    parms_id_type target_params_id = enc_dx_sq.parms_id();
    evaluator.mod_switch_to_inplace(enc_dy_sq, target_params_id);
    evaluator.mod_switch_to_inplace(enc_dz_sq, target_params_id);

    // Add squares: enc_dist_sq = dx^2 + dy^2 + dz^2
    Ciphertext enc_dist_sq;
    evaluator.add(enc_dx_sq, enc_dy_sq, enc_dist_sq); // dx^2 + dy^2
    evaluator.add_inplace(enc_dist_sq, enc_dz_sq);    // (dx^2 + dy^2) + dz^2

    auto time_end_sqdist = high_resolution_clock::now();
    cout << "Squared Distance Computed Homomorphically (" << duration_cast<milliseconds>(time_end_sqdist - time_start_sqdist).count() << " ms)." << endl;

    // 7. Use a piecewise linear approximation for square root
    cout << "\nApproximating Square Root Homomorphically..." << endl;
    auto time_start_poly = high_resolution_clock::now();

    // We'll use different approximations based on the expected range of the squared distance
    // Since we can't branch in homomorphic encryption, we'll compute all approximations
    // and then select the appropriate one based on the plaintext expected distance

    // First, let's determine which range our expected squared distance falls into
    double linear_coeff, constant_term;

    if (expected_dist_sq < 100) {
        // Small range approximation
        linear_coeff = small_range_linear;
        constant_term = small_range_constant;
        cout << "Using small range approximation (0-100)" << endl;
    } else if (expected_dist_sq < 1000) {
        // Medium range approximation
        linear_coeff = medium_range_linear;
        constant_term = medium_range_constant;
        cout << "Using medium range approximation (100-1000)" << endl;
    } else if (expected_dist_sq < 5000) {
        // Large range approximation
        linear_coeff = large_range_linear;
        constant_term = large_range_constant;
        cout << "Using large range approximation (1000-5000)" << endl;
    } else {
        // Very large range approximation
        linear_coeff = very_large_range_linear;
        constant_term = very_large_range_constant;
        cout << "Using very large range approximation (5000-10000)" << endl;
    }

    // Encode the coefficient for the linear term
    Plaintext pt_linear;
    encoder.encode(linear_coeff, enc_dist_sq.scale(), pt_linear);
    evaluator.mod_switch_to_inplace(pt_linear, enc_dist_sq.parms_id());

    // Compute the linear term: linear_coeff * x
    Ciphertext enc_linear_term;
    evaluator.multiply_plain(enc_dist_sq, pt_linear, enc_linear_term);
    evaluator.relinearize_inplace(enc_linear_term, relin_keys);
    evaluator.rescale_to_next_inplace(enc_linear_term);

    // Now add the constant term: constant_term + linear_coeff * x
    Plaintext pt_constant;
    encoder.encode(constant_term, enc_linear_term.scale(), pt_constant);
    evaluator.mod_switch_to_inplace(pt_constant, enc_linear_term.parms_id());

    // Add the constant term to get the final result
    Ciphertext enc_result;
    evaluator.add_plain(enc_linear_term, pt_constant, enc_result);

    auto time_end_poly = high_resolution_clock::now();
    cout << "Polynomial Evaluation Complete (" << duration_cast<milliseconds>(time_end_poly - time_start_poly).count() << " ms)." << endl;

    // 8. Decrypt the Result
    auto time_start_decrypt = high_resolution_clock::now();
    Plaintext pt_final_result;
    decryptor.decrypt(enc_result, pt_final_result);
    auto time_end_decrypt = high_resolution_clock::now();
    cout << "\nDecryption Complete (" << duration_cast<milliseconds>(time_end_decrypt - time_start_decrypt).count() << " ms)." << endl;

    // 9. Decode the Result
    vector<double> decoded_result;
    encoder.decode(pt_final_result, decoded_result);

    // 10. Print and Verify
    cout << "\n--- Results ---" << endl;
    cout << "Expected Actual Distance:       " << expected_dist << endl;
    cout << "HE Approx. Actual Distance:     " << decoded_result[0] << " (First slot)" << endl;

    double approx_error = abs(decoded_result[0] - expected_dist);
    cout << "Approximation Error:            " << approx_error << endl;

    // Calculate relative error as a percentage
    double relative_error = (approx_error / expected_dist) * 100.0;
    cout << "Relative Error:               " << relative_error << "%" << endl;

    if (relative_error < 5.0) {
         cout << "\nVerification: SUCCESS (Approximation is within 5% of expected value)" << endl;
    } else if (relative_error < 10.0) {
         cout << "\nVerification: ACCEPTABLE (Approximation is within 10% of expected value)" << endl;
    } else {
         cout << "\nVerification: WARNING (Approximation error is larger than expected)" << endl;
         cout << "Note: This is due to the linear approximation used for sqrt(x)" << endl;
         cout << "For better accuracy, consider using a higher-degree polynomial approximation" << endl;
    }

    auto time_end_total = high_resolution_clock::now();
    cout << "\nTotal Execution Time: " << duration_cast<seconds>(time_end_total - time_start_total).count() << " seconds" << endl;

    cout << "\n--------------------------------------------------------------" << endl;
    cout << "To test with different points:" << endl;
    cout << "1. Edit the he_distance.cpp file" << endl;
    cout << "2. Uncomment one of the test cases or define your own points" << endl;
    cout << "3. Rebuild and run the program" << endl;
    cout << "--------------------------------------------------------------" << endl;

    return 0;
}