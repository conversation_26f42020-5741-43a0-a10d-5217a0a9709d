cmake_minimum_required(VERSION 3.13) # Or your CMake version
project(HE_Distance_Example LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# --- Find SEAL ---
# Option 1: If you installed SEAL system-wide (e.g., using 'make install')
# find_package(SEAL 4.1 REQUIRED) # Adjust version if needed

# Option 2: If SEAL is built in a specific directory (replace with your path)
# Useful if you built SEAL but didn't install it globally.
# You may need to update this path to where SEAL is installed on your system
set(SEAL_DIR "/usr/local" CACHE PATH "Path to SEAL installation") # Common installation location
find_package(SEAL 4.1 REQUIRED HINTS ${SEAL_DIR}) # Adjust version if needed

if(NOT SEAL_FOUND)
    message(FATAL_ERROR "SEAL library not found. Set SEAL_DIR or install SEAL.")
endif()
message(STATUS "Found SEAL: ${SEAL_INCLUDE_DIRS} ${SEAL_LIBRARIES}") # Debug msg

# --- Build Executable ---
add_executable(he_distance he_distance.cpp)

# Link SEAL library
target_link_libraries(he_distance PRIVATE SEAL::seal)

# Include SEAL headers
target_include_directories(he_distance PRIVATE ${SEAL_INCLUDE_DIRS})