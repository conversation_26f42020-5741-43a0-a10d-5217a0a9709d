#include <iostream>
#include <vector>
#include <cmath>
#include <random>
#include <map>
#include <chrono> // Optional: for seeding random
#include <string> // For std::to_string
#include <complex>
#include <algorithm>
#include <fstream> // For file I/O
#include <queue>
#include <unordered_map>

// Forward declaration
class DroneNetworkSimulator;

// --- Drone Structure ---
struct Drone {
    int id;
    double x;
    double y;
    double z;  // Adding z-coordinate for 3D space
    bool is_base_station; // Flag to identify base stations

    // Channel properties
    double theta;         // Elevation angle
    double prob_los;      // Line-of-sight probability
    double path_loss_exp; // Path loss exponent
    double k_factor;      // Rician K-factor
    std::vector<std::complex<double>> channel; // Channel coefficients

    // Trajectory properties
    double vx;           // Velocity in x direction
    double vy;           // Velocity in y direction
    double vz;           // Velocity in z direction
    double max_speed;    // Maximum speed
    double target_x;     // Target x position
    double target_y;     // Target y position
    double target_z;     // Target z position
    bool has_target;     // Whether drone has a target

    // CSI tracking
    std::map<int, double> neighbor_snr;       // Maps neighbor ID to current SNR
    std::map<int, double> neighbor_path_loss; // Maps neighbor ID to path loss
    std::map<int, double> neighbor_distance;  // Maps neighbor ID to distance
    std::map<int, double> neighbor_doppler;   // Maps neighbor ID to Doppler shift

    // Battery and lifecycle state
    enum DroneState {
        GROUNDED,    // On the ground, not yet taken off
        ASCENDING,   // Taking off
        ACTIVE,      // Normal flight operations
        DESCENDING,  // Landing
        LANDED       // Landed due to battery depletion
    };

    DroneState state;
    double battery_level;       // Percentage (0-100)
    double takeoff_time;        // When this drone should take off
    double battery_capacity;    // Battery capacity in mAh
    double hover_power;         // Power consumption when hovering (W)
    double movement_power;      // Additional power for movement (W)
    double transmission_power;  // Power for data transmission (W)
    double landing_threshold;   // Battery % to trigger landing
    double initial_x;           // Initial x position for return landing
    double initial_y;           // Initial y position for return landing

    // Constructor with z-coordinate
    Drone(int id, double x, double y, double z = 0.0, bool is_base = false)
        : id(id), x(x), y(y), z(z), is_base_station(is_base),
          initial_x(x), initial_y(y),  // Store initial position
          theta(0.0), prob_los(0.0), path_loss_exp(0.0), k_factor(0.0),
          vx(0.0), vy(0.0), vz(0.0), max_speed(5.0),
          target_x(x), target_y(y), target_z(z), has_target(false),
          state(is_base ? GROUNDED : GROUNDED), battery_level(100.0), takeoff_time(0.0),
          battery_capacity(is_base ? 100000.0 : 5000.0), // Base stations have larger battery
          hover_power(100.0),       // 100W for hovering
          movement_power(50.0),     // 50W additional for movement
          transmission_power(is_base ? 0.05 : 0.005),  // Base stations have higher transmission power
          landing_threshold(20.0)   // Land when battery reaches 20%
    {}

    // Calculate 3D Euclidean distance
    double distanceTo(const Drone& other) const {
        double dx = x - other.x;
        double dy = y - other.y;
        double dz = z - other.z;
        return std::sqrt(dx*dx + dy*dy + dz*dz);
    }

    // Calculate horizontal distance
    double horizontalDistanceTo(const Drone& other) const {
        double dx = x - other.x;
        double dy = y - other.y;
        return std::sqrt(dx*dx + dy*dy);
    }

    // Updated string representation
    std::string toString() const {
        std::string stateStr;
        switch(state) {
            case GROUNDED: stateStr = "GROUNDED"; break;
            case ASCENDING: stateStr = "ASCENDING"; break;
            case ACTIVE: stateStr = "ACTIVE"; break;
            case DESCENDING: stateStr = "DESCENDING"; break;
            case LANDED: stateStr = "LANDED"; break;
        }

        std::string prefix = is_base_station ? "Base Station " : "Drone ";
        return prefix + std::to_string(id) +
               " at (" + std::to_string(x) + ", " +
               std::to_string(y) + ", " +
               std::to_string(z) + "), " +
               "State: " + stateStr + ", " +
               "Battery: " + std::to_string(battery_level) + "%";
    }
};

// --- Drone Network Simulator Class ---
class DroneNetworkSimulator {
public:
    // Constructor
    DroneNetworkSimulator(int numDrones, double width, double height, double commRange,
                         double minHeight = 50.0, double maxHeight = 100.0,
                         double simDuration = 3600.0) // 1 hour default simulation
        : simulationTime(0.0),
          worldWidth(width),
          worldHeight(height),
          worldMinHeight(minHeight),
          worldMaxHeight(maxHeight),
          communicationRange(commRange),
          rng(std::chrono::system_clock::now().time_since_epoch().count()),
          // Channel model parameters (from UAV_CHANNEL.py)
          a1(12.08), b1(0.11),
          alpha_zero(3.5), alpha_pi_by_2(2.0),
          k_zero(1.0), k_pi_by_2(15.0),
          fc(2.4e9), // Carrier frequency in Hz
          v(20.0),   // Relative speed in m/s
          c(3.0e8),  // Speed of light in m/s
          ts(1.0e-6) // Symbol duration in seconds
    {
        std::cout << "Initializing Simulator with lifecycle management..." << std::endl;

        // Initialize drones with random positions on the ground (z=0)
        std::uniform_real_distribution<double> distX(0.0, worldWidth);
        std::uniform_real_distribution<double> distY(0.0, worldHeight);

        // Staggered takeoff times (between 0 and 10 seconds)
        std::uniform_real_distribution<double> distTakeoff(0.0, 10.0);

        // Random initial velocity
        std::uniform_real_distribution<double> distV(-2.5, 2.5);

        // Random battery capacity variation (±20%)
        std::uniform_real_distribution<double> distBattery(0.8, 1.2);

        // Create base stations first
        // Transmitter base station at the left side of the world
        transmitter_base_id = 0; // First ID is for transmitter
        drones.emplace_back(transmitter_base_id, 10.0, worldHeight / 2, 0.0, true);
        std::cout << "Created Transmitter Base Station at (10.0, " << worldHeight / 2 << ", 0.0)" << std::endl;

        // Receiver base station at the right side of the world
        receiver_base_id = 1; // Second ID is for receiver
        drones.emplace_back(receiver_base_id, worldWidth - 10.0, worldHeight / 2, 0.0, true);
        std::cout << "Created Receiver Base Station at (" << worldWidth - 10.0 << ", " << worldHeight / 2 << ", 0.0)" << std::endl;

        // Now create the drones with IDs starting from 2
        for (int i = 0; i < numDrones; ++i) {
            // Create drone on the ground (z=0) with ID offset to account for base stations
            drones.emplace_back(i + 2, distX(rng), distY(rng), 0.0);

            // Set random takeoff time
            drones.back().takeoff_time = distTakeoff(rng);

            // Randomize battery capacity slightly
            drones.back().battery_capacity *= distBattery(rng);

            // Set random initial velocity (will be used after takeoff)
            drones.back().vx = distV(rng);
            drones.back().vy = distV(rng);
            drones.back().vz = 0.0; // No vertical velocity while grounded

            // Set initial target for ascent
            drones.back().target_x = drones.back().x;
            drones.back().target_y = drones.back().y;
            drones.back().target_z = worldMinHeight +
                std::uniform_real_distribution<double>(0, worldMaxHeight-worldMinHeight)(rng);
            drones.back().has_target = true;

            std::cout << "Drone " << drones.back().id << " scheduled for takeoff at t="
                      << drones.back().takeoff_time << "s" << std::endl;
        }

        // Initialize channel model and connectivity
        initializeChannelModel();
        updateConnectivity();

        // Initialize AODV routing protocol
        initializeAODV();

        // Initialize metrics logging
        initMetricsLogging();

        // Calculate and print the distance between base stations
        double base_distance = drones[transmitter_base_id].distanceTo(drones[receiver_base_id]);
        double path_loss = calculatePathLoss(base_distance);
        double rx_power_dBm = transmit_power_dBm - path_loss;
        double snr_dB = rx_power_dBm - noise_floor_dBm;

        std::cout << "\n=== BASE STATION CONNECTIVITY ===\n";
        std::cout << "Distance between base stations: " << base_distance << " meters" << std::endl;
        std::cout << "Path loss: " << path_loss << " dB" << std::endl;
        std::cout << "Received power: " << rx_power_dBm << " dBm" << std::endl;
        std::cout << "SNR: " << snr_dB << " dB" << std::endl;
        std::cout << "SNR threshold: " << snr_threshold_dB << " dB" << std::endl;
        std::cout << "Direct connection possible: " << (snr_dB >= snr_threshold_dB ? "YES" : "NO") << std::endl;
        std::cout << "================================\n";
    }

    // --- Main Simulation Function ---
    void runSimulation(int steps, double timeStep, bool log_csi = true) {
        std::cout << "Starting simulation with " << drones.size() << " drones..." << std::endl;

        // Initialize CSI logging if requested
        if (log_csi) {
            initCSILogging();
        }

        // Print initial state
        printStatus();

        // Log initial CSI data
        if (log_csi) {
            logCSIData();
        }

        // Run simulation steps
        for (int i = 0; i < steps; ++i) {
            tick(timeStep);
            printStatus();

            // Log CSI data after each step
            if (log_csi) {
                logCSIData();
            }
        }

        // Close CSI logging
        if (log_csi) {
            closeCSILogging();
        }

        // Close metrics logging
        closeMetricsLogging();

        std::cout << "Simulation completed." << std::endl;
    }


    // --- Getters (Optional but good practice) ---
    const std::vector<Drone>& getDrones() const {
        return drones;
    }

    const std::map<int, std::vector<int>>& getConnectivity() const {
        return connectivityMap;
    }

    double getCurrentTime() const {
        return simulationTime;
    }

    // Create a test packet from transmitter base station to receiver base station
    void createTestPacket() {
        // Create a packet from transmitter base to receiver base
        std::cout << "\n=== CREATING TEST PACKET ===\n";
        std::cout << "Transmitter ID: " << transmitter_base_id << ", Receiver ID: " << receiver_base_id << std::endl;

        // Check if there's a direct connection
        bool direct_connection = false;
        auto it = connectivityMap.find(transmitter_base_id);
        if (it != connectivityMap.end()) {
            direct_connection = std::find(it->second.begin(), it->second.end(), receiver_base_id) != it->second.end();
        }
        std::cout << "Direct connection exists: " << (direct_connection ? "YES" : "NO") << std::endl;

        if (direct_connection) {
            std::cout << "Direct connection between base stations will be forbidden in routing" << std::endl;
        }

        // Find path using hybrid A*-AODV approach
        std::vector<int> route = findRouteHybrid(transmitter_base_id, receiver_base_id);
        std::cout << "Hybrid A*-AODV path: ";
        if (route.empty()) {
            std::cout << "NO PATH FOUND";
        } else {
            for (size_t i = 0; i < route.size(); ++i) {
                std::cout << route[i];
                if (i < route.size() - 1) std::cout << " -> ";
            }

            // Calculate and print path metrics
            double total_distance = 0.0;
            for (size_t i = 0; i < route.size() - 1; ++i) {
                int drone1_idx = -1, drone2_idx = -1;
                for (size_t j = 0; j < drones.size(); j++) {
                    if (drones[j].id == route[i]) drone1_idx = j;
                    if (drones[j].id == route[i+1]) drone2_idx = j;
                }

                if (drone1_idx != -1 && drone2_idx != -1) {
                    double hop_distance = drones[drone1_idx].distanceTo(drones[drone2_idx]);
                    total_distance += hop_distance;
                }
            }

            std::cout << "\nPath length: " << route.size() << " hops";
            std::cout << ", Total distance: " << total_distance << " meters";
            std::cout << ", Average hop distance: " << (total_distance / (route.size() - 1)) << " meters";
        }
        std::cout << std::endl;

        // Create the packet
        createPacket(transmitter_base_id, receiver_base_id);
        std::cout << "Created test packet from Transmitter Base Station to Receiver Base Station\n";
        std::cout << "==============================\n";
    }

    // Routing metrics structure
    struct RoutingMetrics {
        double total_end_to_end_latency;
        int total_packets_delivered;
        double total_astar_execution_time;
        int total_astar_executions;
        int total_rreq_sent;
        int total_rrep_sent;
        int total_rerr_sent;
        int total_route_discoveries;
        int total_route_failures;

        RoutingMetrics()
            : total_end_to_end_latency(0.0), total_packets_delivered(0),
              total_astar_execution_time(0.0), total_astar_executions(0),
              total_rreq_sent(0), total_rrep_sent(0), total_rerr_sent(0),
              total_route_discoveries(0), total_route_failures(0) {}

        double getAverageLatency() const {
            return total_packets_delivered > 0 ?
                   total_end_to_end_latency / total_packets_delivered : 0.0;
        }

        double getAverageAStarTime() const {
            return total_astar_executions > 0 ?
                   total_astar_execution_time / total_astar_executions : 0.0;
        }

        double getAverageRouteDiscoveryTime() const {
            return total_route_discoveries > 0 ?
                   (total_rreq_sent + total_rrep_sent) / total_route_discoveries : 0.0;
        }
    };

    // Get routing metrics
    const RoutingMetrics& getRoutingMetrics() const {
        return routing_metrics;
    }

private:
    std::vector<Drone> drones;
    int transmitter_base_id; // ID of the transmitter base station
    int receiver_base_id;   // ID of the receiver base station
    double simulationTime;
    double worldWidth;
    double worldHeight;
    double worldMinHeight;
    double worldMaxHeight;
    double communicationRange;
    std::map<int, std::vector<int>> connectivityMap; // Drone ID -> vector of neighbor IDs

    // Random number generation
    std::mt19937 rng;

    // Channel model parameters
    double a1, b1;
    double alpha_zero, alpha_pi_by_2;
    double k_zero, k_pi_by_2;
    double fc, v, c, ts;

    // Communication parameters
    double transmit_power_dBm = 20.0;  // Transmit power in dBm (20 dBm = 100 mW)
    double noise_floor_dBm = -90.0;    // Noise floor in dBm
    double snr_threshold_dB = -10.0;   // Lower threshold to allow more connections

    // File for logging CSI data
    std::ofstream csi_log_file;

    // AODV routing protocol structures
    struct AODVRouteEntry {
        int destination_id;       // Destination node ID
        int next_hop_id;          // Next hop node ID
        int hop_count;            // Number of hops to destination
        int sequence_number;      // Destination sequence number
        double lifetime;          // Route expiration time
        bool valid;               // Whether the route is valid

        AODVRouteEntry(int dst, int next, int hops, int seq, double life)
            : destination_id(dst), next_hop_id(next), hop_count(hops),
              sequence_number(seq), lifetime(life), valid(true) {}

        AODVRouteEntry()
            : destination_id(-1), next_hop_id(-1), hop_count(0),
              sequence_number(0), lifetime(0.0), valid(false) {}
    };

    struct AODVRouteRequest {
        int rreq_id;              // RREQ ID
        int source_id;            // Source node ID
        int destination_id;       // Destination node ID
        int source_sequence;      // Source sequence number
        int destination_sequence; // Destination sequence number
        int hop_count;            // Number of hops from source
        double timestamp;         // When the RREQ was created

        AODVRouteRequest(int id, int src, int dst, int src_seq, int dst_seq, int hops, double time)
            : rreq_id(id), source_id(src), destination_id(dst),
              source_sequence(src_seq), destination_sequence(dst_seq),
              hop_count(hops), timestamp(time) {}
    };

    struct AODVRouteReply {
        int source_id;            // Source node ID
        int destination_id;       // Destination node ID
        int destination_sequence; // Destination sequence number
        int hop_count;            // Number of hops to destination
        double lifetime;          // Route lifetime
        double timestamp;         // When the RREP was created

        AODVRouteReply(int src, int dst, int seq, int hops, double life, double time)
            : source_id(src), destination_id(dst), destination_sequence(seq),
              hop_count(hops), lifetime(life), timestamp(time) {}
    };

    struct AODVRouteError {
        int source_id;            // Source node ID
        int destination_id;       // Destination node ID
        int destination_sequence; // Destination sequence number
        double timestamp;         // When the RERR was created

        AODVRouteError(int src, int dst, int seq, double time)
            : source_id(src), destination_id(dst), destination_sequence(seq),
              timestamp(time) {}
    };

    // Add packet routing structures to the simulator class
    struct RoutingPacket {
        int source_id;
        int destination_id;
        int current_node_id;
        double creation_time;
        double last_hop_time;
        std::vector<int> path;
        bool delivered;

        RoutingPacket(int src, int dst, double time)
            : source_id(src), destination_id(dst), current_node_id(src),
              creation_time(time), last_hop_time(time), delivered(false) {
            path.push_back(src);
        }
    };

    // Routing metrics is now defined in the public section

    // Add to private members of DroneNetworkSimulator
    std::vector<RoutingPacket> active_packets;
    RoutingMetrics routing_metrics;
    const double PACKET_PROCESSING_POWER = 15.0; // 15 watt-hours per packet

    // AODV routing protocol parameters
    const double AODV_ROUTE_DISCOVERY_TIMEOUT = 2.0;  // Seconds
    const double AODV_ROUTE_LIFETIME = 10.0;          // Seconds
    const double AODV_ACTIVE_ROUTE_TIMEOUT = 5.0;     // Seconds
    const int AODV_TTL_START = 1;                     // Initial TTL value
    const int AODV_TTL_INCREMENT = 2;                 // TTL increment
    const int AODV_TTL_THRESHOLD = 7;                 // TTL threshold
    const int AODV_NET_DIAMETER = 35;                 // Maximum network diameter

    // AODV routing tables and message queues
    std::map<int, std::map<int, AODVRouteEntry>> route_tables;  // Node ID -> (Destination ID -> Route Entry)
    std::map<int, int> sequence_numbers;                        // Node ID -> Sequence Number
    std::vector<AODVRouteRequest> pending_route_requests;       // Pending RREQs
    std::vector<AODVRouteReply> pending_route_replies;         // Pending RREPs
    std::vector<AODVRouteError> pending_route_errors;          // Pending RERRs
    std::map<int, std::map<std::pair<int, int>, double>> rreq_seen;  // Node ID -> ((Source ID, RREQ ID) -> Timestamp)

    // File for logging metrics data
    std::ofstream metrics_log_file;

    // Initialize metrics logging
    void initMetricsLogging() {
        metrics_log_file.open("A_star_routing_metrics.csv");
        if (metrics_log_file.is_open()) {
            metrics_log_file << "Time,PacketsDelivered,AverageLatency,AverageAStarTime,ActivePackets,SourceID,DestID,Path,RREQ,RREP,RERR,RouteDiscoveries,RouteFailures\n";
        } else {
            std::cerr << "Failed to open metrics log file" << std::endl;
        }
    }

    // Initialize AODV routing protocol
    void initializeAODV() {
        std::cout << "Initializing AODV routing protocol..." << std::endl;

        // Initialize sequence numbers for all drones
        for (const auto& drone : drones) {
            sequence_numbers[drone.id] = 1; // Start with sequence number 1
        }

        // Clear routing tables
        route_tables.clear();

        // Clear pending messages
        pending_route_requests.clear();
        pending_route_replies.clear();
        pending_route_errors.clear();
        rreq_seen.clear();

        std::cout << "AODV routing protocol initialized." << std::endl;
    }

    // Log metrics data
    void logMetricsData() {
        if (metrics_log_file.is_open()) {
            // If no active packets, log basic metrics
            if (active_packets.empty()) {
                metrics_log_file << simulationTime << ","
                                << routing_metrics.total_packets_delivered << ","
                                << routing_metrics.getAverageLatency() << ","
                                << routing_metrics.getAverageAStarTime() << ","
                                << "0,,,,"
                                << routing_metrics.total_rreq_sent << ","
                                << routing_metrics.total_rrep_sent << ","
                                << routing_metrics.total_rerr_sent << ","
                                << routing_metrics.total_route_discoveries << ","
                                << routing_metrics.total_route_failures << "\n";
            } else {
                // Log each active packet with its path
                for (const auto& packet : active_packets) {
                    metrics_log_file << simulationTime << ","
                                    << routing_metrics.total_packets_delivered << ","
                                    << routing_metrics.getAverageLatency() << ","
                                    << routing_metrics.getAverageAStarTime() << ","
                                    << active_packets.size() << ","
                                    << packet.source_id << ","
                                    << packet.destination_id << ",\"";

                    // Add path as a string
                    for (size_t i = 0; i < packet.path.size(); ++i) {
                        metrics_log_file << packet.path[i];
                        if (i < packet.path.size() - 1) {
                            metrics_log_file << "->";
                        }
                    }
                    metrics_log_file << "\","
                                    << routing_metrics.total_rreq_sent << ","
                                    << routing_metrics.total_rrep_sent << ","
                                    << routing_metrics.total_rerr_sent << ","
                                    << routing_metrics.total_route_discoveries << ","
                                    << routing_metrics.total_route_failures << "\n";
                }
            }
        }
    }

    // Close metrics logging
    void closeMetricsLogging() {
        if (metrics_log_file.is_open()) {
            metrics_log_file.close();
        }
    }

    // --- Simulation Step Logic ---
    void tick(double timeStep) {
        simulationTime += timeStep;
        std::cout << "--- Time Step: " << simulationTime << " ---" << std::endl;

        // Process drone lifecycle and movement
        manageDroneLifecycle(timeStep);
        moveDrones(timeStep);
        enforceBounds();

        // Update channel model and connectivity
        initializeChannelModel();
        updateConnectivity();
        printConnectivityMap();

        // Process AODV routing messages
        processAODVMessages();

        // Process routing packets
        processPackets(timeStep);

        // Log data
        logCSIData();
        logMetricsData();

        // Periodically print routing metrics
        if (std::fmod(simulationTime, 10.0) < timeStep) {
            printRoutingMetrics();
        }

        // Periodically create test packets (every 5 seconds)
        if (std::fmod(simulationTime, 5.0) < timeStep) {
            createTestPacket();
        }
    }

    // --- Helper Functions ---

    // Set a random target for a drone
    void setRandomTarget(Drone& drone) {
        std::uniform_real_distribution<double> distX(0.0, worldWidth);
        std::uniform_real_distribution<double> distY(0.0, worldHeight);
        std::uniform_real_distribution<double> distZ(worldMinHeight, worldMaxHeight);

        drone.target_x = distX(rng);
        drone.target_y = distY(rng);
        drone.target_z = distZ(rng);
        drone.has_target = true;

        // Debug output
        // std::cout << "Drone " << drone.id << " new target: ("
        //           << drone.target_x << ", " << drone.target_y << ", "
        //           << drone.target_z << ")" << std::endl;
    }

    // Update drone positions (simple random walk)
    void moveDrones(double timeStep) {
        const double acceleration = 2.0;
        const double target_threshold = 2.0;

        for (auto& drone : drones) {
            // Skip drones that are grounded, landed, or base stations
            if (drone.state == Drone::GROUNDED || drone.state == Drone::LANDED || drone.is_base_station) {
                continue;
            }

            // If drone has no target or has reached its target, set a new one (only for ACTIVE drones)
            if (drone.state == Drone::ACTIVE &&
                (!drone.has_target ||
                 (std::abs(drone.x - drone.target_x) < target_threshold &&
                  std::abs(drone.y - drone.target_y) < target_threshold &&
                  std::abs(drone.z - drone.target_z) < target_threshold))) {
                setRandomTarget(drone);
            }

            // Calculate direction vector to target
            double dx = drone.target_x - drone.x;
            double dy = drone.target_y - drone.y;
            double dz = drone.target_z - drone.z;

            // Normalize direction vector
            double distance = std::sqrt(dx*dx + dy*dy + dz*dz);
            if (distance > 0) {
                dx /= distance;
                dy /= distance;
                dz /= distance;
            }

            // Apply acceleration in the direction of the target
            drone.vx += dx * acceleration * timeStep;
            drone.vy += dy * acceleration * timeStep;
            drone.vz += dz * acceleration * timeStep;

            // For ascending/descending drones, prioritize vertical movement
            if (drone.state == Drone::ASCENDING || drone.state == Drone::DESCENDING) {
                drone.vx *= 0.5;  // Reduce horizontal movement
                drone.vy *= 0.5;

                // Ensure vertical speed is appropriate
                if (drone.state == Drone::ASCENDING) {
                    drone.vz = std::max(drone.vz, 2.0);  // Minimum ascent speed
                } else {
                    drone.vz = std::min(drone.vz, -1.0);  // Maximum descent speed (slower for safety)
                }
            }

            // Limit speed to max_speed
            double speed = std::sqrt(drone.vx*drone.vx + drone.vy*drone.vy + drone.vz*drone.vz);
            if (speed > drone.max_speed) {
                double factor = drone.max_speed / speed;
                drone.vx *= factor;
                drone.vy *= factor;
                drone.vz *= factor;
            }

            // Update position
            drone.x += drone.vx * timeStep;
            drone.y += drone.vy * timeStep;
            drone.z += drone.vz * timeStep;
        }
    }

    // Keep drones within defined world boundaries (simple bounce back)
    void enforceBounds() {
        for (auto& drone : drones) {
            if (drone.x < 0) { drone.x = 0; }
            else if (drone.x > worldWidth) { drone.x = worldWidth; }

            if (drone.y < 0) { drone.y = 0; }
            else if (drone.y > worldHeight) { drone.y = worldHeight; }

            if (drone.z < worldMinHeight) { drone.z = worldMinHeight; }
            else if (drone.z > worldMaxHeight) { drone.z = worldMaxHeight; }
        }
    }

    // Recalculate which drones are within communication range of each other using SNR
    void updateConnectivity() {
        // Clear previous connectivity map
        connectivityMap.clear();

        // Debug output
        std::cout << "Updating connectivity map..." << std::endl;

        // For each drone, determine which other drones are in communication range
        for (size_t i = 0; i < drones.size(); i++) {
            for (size_t j = 0; j < drones.size(); j++) {
                if (i == j) continue;

                // Prevent direct connection between base stations
                if (drones[i].is_base_station && drones[j].is_base_station) {
                    std::cout << "Preventing direct connection between base stations: "
                              << drones[i].id << " -> " << drones[j].id << std::endl;
                    continue;
                }

                double distance = drones[i].distanceTo(drones[j]);
                double path_loss = calculatePathLoss(distance);
                double rx_power_dBm = transmit_power_dBm - path_loss;
                double snr_dB = rx_power_dBm - noise_floor_dBm;

                // Adjust SNR threshold for base stations to ensure they connect to drones
                double effective_snr_threshold = snr_threshold_dB;
                if (drones[i].is_base_station || drones[j].is_base_station) {
                    // Make it easier for base stations to connect to drones
                    effective_snr_threshold -= 5.0; // 5dB bonus for base station connections
                }

                if (snr_dB >= effective_snr_threshold) {
                    connectivityMap[drones[i].id].push_back(drones[j].id);
                    std::cout << "Connection established: " << drones[i].id << " -> " << drones[j].id
                              << " (SNR: " << snr_dB << " dB)" << std::endl;
                }
            }
        }

        // Print connectivity map size for debugging
        std::cout << "Connectivity map size: " << connectivityMap.size() << " nodes" << std::endl;
    }

    // Initialize the channel model for all drones
    void initializeChannelModel() {
        // Calculate LOS probability constants
        double prob_los_pi_by_2 = 1.0 / (1.0 + a1 * std::exp(-b1 * (M_PI/2.0 - a1)));
        double prob_los_zero = 1.0 / (1.0 + a1 * std::exp(-b1 * (0.0 - a1)));

        // Calculate path loss exponent constants
        double a2 = (alpha_pi_by_2 - alpha_zero) / (prob_los_pi_by_2 - prob_los_zero);
        double b2 = alpha_zero - a2 * prob_los_zero;

        // Calculate K-factor constants
        double a3 = k_zero;
        double b3 = (2.0/M_PI) * std::log(k_pi_by_2/k_zero);

        // Calculate Doppler shift
        double f_doppler = v * fc / c;

        // For each drone, calculate channel parameters
        for (auto& drone : drones) {
            // Calculate elevation angle (theta)
            double d_h = std::sqrt(drone.x * drone.x + drone.y * drone.y);
            double d_v = drone.z;
            drone.theta = std::atan2(d_v, d_h);

            // Calculate LOS probability
            drone.prob_los = 1.0 / (1.0 + a1 * std::exp(-b1 * (drone.theta - a1)));

            // Calculate path loss exponent
            drone.path_loss_exp = a2 * drone.prob_los + b2;

            // Calculate Rician K-factor
            drone.k_factor = a3 * std::exp(b3 * drone.theta);

            // Generate channel coefficients (simplified)
            generateChannelCoefficients(drone);
        }
    }

    // Generate channel coefficients for a drone
    void generateChannelCoefficients(Drone& drone, int num_realizations = 1000) {
        // Channel model parameters
        double p_los_plus_nlos = 1.0;
        double sigma = p_los_plus_nlos / std::sqrt(2.0 * (drone.k_factor + 1.0));
        double s = std::sqrt(drone.k_factor / (drone.k_factor + 1.0) * p_los_plus_nlos);

        // Normal distribution for generating random components
        std::normal_distribution<double> normal_dist(0.0, 1.0);

        // Calculate Doppler shift
        double f_doppler = v * fc / c;

        // Generate channel coefficients
        drone.channel.resize(num_realizations);
        for (int i = 0; i < num_realizations; ++i) {
            // Generate real and imaginary parts
            double h_real = sigma * normal_dist(rng) + s;
            double h_imag = sigma * normal_dist(rng);

            // Create complex channel coefficient
            std::complex<double> h(h_real, h_imag);

            // Apply Doppler shift
            double phase = -2.0 * M_PI * f_doppler * ts;
            std::complex<double> doppler_factor(std::cos(phase), std::sin(phase));
            drone.channel[i] = h * doppler_factor;
        }
    }

    // Print the current state of the network
    void printStatus() const {
        std::cout << "Current State (Time: " << simulationTime << "):" << std::endl;
        for (const auto& drone : drones) {
            std::cout << "  " << drone.toString();

            // Print velocity
            double speed = std::sqrt(drone.vx*drone.vx + drone.vy*drone.vy + drone.vz*drone.vz);
            std::cout << " Speed: " << speed << " m/s";

            // Print neighbors
            auto it = connectivityMap.find(drone.id);
            if (it != connectivityMap.end()) {
                std::cout << " -> Neighbors: [";
                const auto& neighbors = it->second;
                for (size_t k = 0; k < neighbors.size(); ++k) {
                    std::cout << neighbors[k] << (k == neighbors.size() - 1 ? "" : ", ");
                }
                std::cout << "]" << std::endl;
            } else {
                std::cout << " -> Neighbors: []" << std::endl;
            }
        }
    }

    // Initialize CSI logging
    void initCSILogging(const std::string& filename = "A_star_csi_data.csv") {
        csi_log_file.open(filename);
        if (csi_log_file.is_open()) {
            // Write CSV header
            csi_log_file << "time,source_id,dest_id,distance,snr,path_loss,doppler_shift,";
            csi_log_file << "source_x,source_y,source_z,dest_x,dest_y,dest_z,";
            csi_log_file << "source_vx,source_vy,source_vz,dest_vx,dest_vy,dest_vz,";
            csi_log_file << "connected\n";
        }
    }

    // Log CSI data for current time step
    void logCSIData() {
        if (!csi_log_file.is_open()) return;

        for (const auto& source : drones) {
            for (const auto& dest : drones) {
                if (source.id == dest.id) continue;

                // Check if this is a connected pair
                bool connected = false;
                auto it = connectivityMap.find(source.id);
                if (it != connectivityMap.end()) {
                    connected = std::find(it->second.begin(), it->second.end(), dest.id) != it->second.end();
                }

                // Get CSI data - use find() instead of operator[] for const maps
                double snr = 0.0, path_loss = 0.0, distance = 0.0, doppler = 0.0;

                auto snr_it = source.neighbor_snr.find(dest.id);
                if (snr_it != source.neighbor_snr.end()) {
                    snr = snr_it->second;
                }

                auto path_loss_it = source.neighbor_path_loss.find(dest.id);
                if (path_loss_it != source.neighbor_path_loss.end()) {
                    path_loss = path_loss_it->second;
                }

                auto distance_it = source.neighbor_distance.find(dest.id);
                if (distance_it != source.neighbor_distance.end()) {
                    distance = distance_it->second;
                }

                auto doppler_it = source.neighbor_doppler.find(dest.id);
                if (doppler_it != source.neighbor_doppler.end()) {
                    doppler = doppler_it->second;
                }

                // Write to CSV
                csi_log_file << simulationTime << ",";
                csi_log_file << source.id << "," << dest.id << ",";
                csi_log_file << distance << "," << snr << "," << path_loss << "," << doppler << ",";
                csi_log_file << source.x << "," << source.y << "," << source.z << ",";
                csi_log_file << dest.x << "," << dest.y << "," << dest.z << ",";
                csi_log_file << source.vx << "," << source.vy << "," << source.vz << ",";
                csi_log_file << dest.vx << "," << dest.vy << "," << dest.vz << ",";
                csi_log_file << (connected ? "1" : "0") << "\n";
            }
        }
        csi_log_file.flush();
    }

    // Close CSI logging
    void closeCSILogging() {
        if (csi_log_file.is_open()) {
            csi_log_file.close();
        }
    }

    // New method to manage drone lifecycle
    void manageDroneLifecycle(double timeStep) {
        for (auto& drone : drones) {
            // Skip drones that have permanently landed or are base stations
            if (drone.state == Drone::LANDED || drone.is_base_station) {
                continue;
            }

            // Handle takeoff sequence
            if (drone.state == Drone::GROUNDED && simulationTime >= drone.takeoff_time) {
                drone.state = Drone::ASCENDING;
                drone.target_z = worldMinHeight;
                drone.has_target = true;
                std::cout << "Drone " << drone.id << " initiating takeoff" << std::endl;
            }

            // Handle state transitions
            if (drone.state == Drone::ASCENDING && drone.z >= worldMinHeight) {
                drone.state = Drone::ACTIVE;
                setRandomTarget(drone);
                std::cout << "Drone " << drone.id << " reached operational altitude" << std::endl;
            }

            // Calculate power consumption and update battery
            double power_used = calculatePowerConsumption(drone, timeStep);
            updateBattery(drone, power_used, timeStep);

            // Check if battery is low and drone needs to land
            if (drone.state == Drone::ACTIVE && drone.battery_level <= drone.landing_threshold) {
                drone.state = Drone::DESCENDING;
                drone.target_z = 0.0;  // Target ground level
                drone.target_x = drone.initial_x;  // Return to takeoff location
                drone.target_y = drone.initial_y;
                drone.has_target = true;
                std::cout << "Drone " << drone.id << " battery low ("
                          << drone.battery_level << "%), returning to launch position" << std::endl;
            }

            // Check if drone has landed
            if (drone.state == Drone::DESCENDING && drone.z <= 0.1) {
                drone.z = 0.0;
                drone.vx = 0.0;
                drone.vy = 0.0;
                drone.vz = 0.0;
                drone.state = Drone::LANDED;
                std::cout << "Drone " << drone.id << " has landed with "
                          << drone.battery_level << "% battery remaining" << std::endl;
            }
        }
    }

    // Calculate power consumption based on drone state and activity
    double calculatePowerConsumption(const Drone& drone, double timeStep) {
        // Base power consumption (in Watts)
        double power = 0.0;

        // Only consume power if not grounded or landed
        if (drone.state != Drone::GROUNDED && drone.state != Drone::LANDED) {
            // Base hover power
            power += drone.hover_power;

            // Additional power for movement (proportional to velocity)
            double speed = std::sqrt(drone.vx*drone.vx + drone.vy*drone.vy + drone.vz*drone.vz);
            double movement_factor = speed / drone.max_speed;

            // Adjust power based on state
            if (drone.state == Drone::DESCENDING) {
                // Descending uses less power than hovering (gravity assists)
                power = drone.hover_power * 0.7;  // 70% of hover power for descent
                power += drone.movement_power * movement_factor * 0.5;  // Less power for movement during descent
            } else {
                // Normal power calculation for ASCENDING and ACTIVE states
                power += drone.movement_power * movement_factor;
            }

            // Power for data transmission (if connected to other drones)
            auto it = connectivityMap.find(drone.id);
            if (it != connectivityMap.end() && !it->second.empty()) {
                power += drone.transmission_power * it->second.size();
            }
        }

        return power;
    }

    // Update battery level based on power consumption
    void updateBattery(Drone& drone, double power, double timeStep) {
        // Skip if drone is grounded or landed
        if (drone.state == Drone::GROUNDED || drone.state == Drone::LANDED) {
            return;
        }

        // Convert power (W) and time (s) to energy (mAh)
        // Assuming battery voltage of 11.1V (typical for drones)
        double voltage = 11.1;
        double energy_consumed_mAh = (power * timeStep) / (voltage * 3.6); // 3.6 for W*s to mAh conversion

        // Update battery level
        double percentage_consumed = (energy_consumed_mAh / drone.battery_capacity) * 100.0;
        drone.battery_level -= percentage_consumed;

        // Ensure battery doesn't go below 0
        if (drone.battery_level < 0) {
            drone.battery_level = 0;
        }
    }

    // A* routing algorithm implementation
    std::vector<int> findRouteAStar(int source_id, int destination_id) {
        auto start_time = std::chrono::high_resolution_clock::now();

        // Find source and destination indices
        int source_idx = -1, dest_idx = -1;
        for (size_t i = 0; i < drones.size(); i++) {
            if (drones[i].id == source_id) source_idx = i;
            if (drones[i].id == destination_id) dest_idx = i;
        }

        if (source_idx == -1 || dest_idx == -1) {
            return std::vector<int>();
        }

        // If direct connection exists, return simple path (but not for base station to base station)
        auto it = connectivityMap.find(source_id);
        if (it != connectivityMap.end() &&
            std::find(it->second.begin(), it->second.end(), destination_id) != it->second.end()) {

            // Check if this is a direct connection between base stations
            bool is_base_to_base = (source_id == transmitter_base_id && destination_id == receiver_base_id) ||
                                  (source_id == receiver_base_id && destination_id == transmitter_base_id);

            // Only use direct path if it's not between base stations
            if (!is_base_to_base) {
                std::vector<int> direct_path = {source_id, destination_id};

                auto end_time = std::chrono::high_resolution_clock::now();
                std::chrono::duration<double, std::milli> duration = end_time - start_time;
                routing_metrics.total_astar_execution_time += duration.count() / 1000.0; // Convert to seconds
                routing_metrics.total_astar_executions++;

                // Consume battery for A* computation (minimal for direct path)
                if (source_idx >= 0 && source_idx < drones.size()) {
                    double computation_power = 0.05; // 0.05W for simple path finding
                    double energy_mah = (computation_power * 0.01) / (11.1 * 3.6); // Assuming 10ms of computation
                    double percentage = (energy_mah / drones[source_idx].battery_capacity) * 100.0;
                    drones[source_idx].battery_level -= percentage;
                }

                return direct_path;
            } else {
                std::cout << "Preventing direct path between base stations, forcing relay through drones" << std::endl;
            }
        }

        // A* algorithm implementation
        std::priority_queue<std::pair<double, int>,
                            std::vector<std::pair<double, int>>,
                            std::greater<std::pair<double, int>>> open_set;

        std::unordered_map<int, int> came_from;
        std::unordered_map<int, double> g_score;
        std::unordered_map<int, double> f_score;

        // Initialize scores
        for (const auto& drone : drones) {
            g_score[drone.id] = std::numeric_limits<double>::infinity();
            f_score[drone.id] = std::numeric_limits<double>::infinity();
        }

        g_score[source_id] = 0;
        f_score[source_id] = drones[source_idx].distanceTo(drones[dest_idx]);

        open_set.push(std::make_pair(f_score[source_id], source_id));

        while (!open_set.empty()) {
            int current = open_set.top().second;
            open_set.pop();

            if (current == destination_id) {
                // Reconstruct path
                std::vector<int> path;
                while (current != source_id) {
                    path.push_back(current);
                    current = came_from[current];
                }
                path.push_back(source_id);
                std::reverse(path.begin(), path.end());

                auto end_time = std::chrono::high_resolution_clock::now();
                std::chrono::duration<double, std::milli> duration = end_time - start_time;
                routing_metrics.total_astar_execution_time += duration.count() / 1000.0;
                routing_metrics.total_astar_executions++;

                return path;
            }

            // Get neighbors from connectivity map
            auto neighbors_it = connectivityMap.find(current);
            if (neighbors_it == connectivityMap.end()) continue;

            for (int neighbor : neighbors_it->second) {
                // Find drone indices
                int current_idx = -1, neighbor_idx = -1;
                for (size_t i = 0; i < drones.size(); i++) {
                    if (drones[i].id == current) current_idx = i;
                    if (drones[i].id == neighbor) neighbor_idx = i;
                }

                if (current_idx == -1 || neighbor_idx == -1) continue;

                // Calculate tentative g_score
                double tentative_g_score = g_score[current] +
                                          drones[current_idx].distanceTo(drones[neighbor_idx]);

                if (tentative_g_score < g_score[neighbor]) {
                    came_from[neighbor] = current;
                    g_score[neighbor] = tentative_g_score;
                    f_score[neighbor] = g_score[neighbor] +
                                       drones[neighbor_idx].distanceTo(drones[dest_idx]);

                    // Check if neighbor is in open set
                    bool in_open_set = false;
                    std::priority_queue<std::pair<double, int>,
                                       std::vector<std::pair<double, int>>,
                                       std::greater<std::pair<double, int>>> temp = open_set;
                    while (!temp.empty()) {
                        if (temp.top().second == neighbor) {
                            in_open_set = true;
                            break;
                        }
                        temp.pop();
                    }

                    if (!in_open_set) {
                        open_set.push(std::make_pair(f_score[neighbor], neighbor));
                    }
                }
            }
        }

        auto end_time = std::chrono::high_resolution_clock::now();
        std::chrono::duration<double, std::milli> duration = end_time - start_time;
        double execution_time_s = duration.count() / 1000.0;

        // Consume battery for A* computation (more for complex paths)
        if (source_idx >= 0 && source_idx < drones.size()) {
            // Power consumption increases with execution time
            double computation_power = 0.2 + (0.1 * execution_time_s); // 0.2W base + 0.1W per second
            double energy_mah = (computation_power * execution_time_s) / (11.1 * 3.6);
            double percentage = (energy_mah / drones[source_idx].battery_capacity) * 100.0;
            drones[source_idx].battery_level -= percentage;
        }

        routing_metrics.total_astar_execution_time += execution_time_s;
        routing_metrics.total_astar_executions++;

        // Return empty path if no route found
        return std::vector<int>();
    }

    // AODV Route Discovery
    void initiateRouteDiscovery(int source_id, int destination_id) {
        std::cout << "Initiating AODV route discovery from " << source_id << " to " << destination_id << std::endl;

        // Increment source sequence number
        sequence_numbers[source_id]++;

        // Create a new RREQ
        static int rreq_id_counter = 0;
        int rreq_id = rreq_id_counter++;

        // Get destination sequence number if available
        int dest_seq = 0;
        for (const auto& node_routes : route_tables) {
            auto it = node_routes.second.find(destination_id);
            if (it != node_routes.second.end() && it->second.valid) {
                dest_seq = std::max(dest_seq, it->second.sequence_number);
            }
        }

        // Create and broadcast RREQ
        AODVRouteRequest rreq(rreq_id, source_id, destination_id,
                             sequence_numbers[source_id], dest_seq,
                             0, simulationTime);

        broadcastRREQ(rreq, source_id);

        // Update metrics
        routing_metrics.total_route_discoveries++;
    }

    // Broadcast RREQ to neighbors
    void broadcastRREQ(const AODVRouteRequest& rreq, int node_id) {
        // Get neighbors of the node
        auto it = connectivityMap.find(node_id);
        if (it == connectivityMap.end()) {
            return; // No neighbors
        }

        // Create a copy of RREQ with incremented hop count
        AODVRouteRequest new_rreq = rreq;
        new_rreq.hop_count++;

        // Add to pending RREQs
        pending_route_requests.push_back(new_rreq);

        // Mark as seen by this node
        rreq_seen[node_id][std::make_pair(rreq.source_id, rreq.rreq_id)] = simulationTime;

        // Update metrics
        routing_metrics.total_rreq_sent++;

        std::cout << "Node " << node_id << " broadcasting RREQ " << rreq.rreq_id
                  << " for destination " << rreq.destination_id << std::endl;
    }

    // Process RREQ at a node
    void processRREQ(const AODVRouteRequest& rreq, int node_id) {
        std::cout << "Node " << node_id << " processing RREQ from " << rreq.source_id
                  << " to " << rreq.destination_id << " (ID: " << rreq.rreq_id
                  << ", hops: " << rreq.hop_count << ")" << std::endl;

        // Check if this is a direct path between base stations
        bool is_base_to_base = (rreq.source_id == transmitter_base_id && rreq.destination_id == receiver_base_id) ||
                              (rreq.source_id == receiver_base_id && rreq.destination_id == transmitter_base_id);

        if (is_base_to_base) {
            std::cout << "RREQ is for a path between base stations, must use drone relays" << std::endl;
        }

        // Check if this RREQ has been seen before
        auto it = rreq_seen[node_id].find(std::make_pair(rreq.source_id, rreq.rreq_id));
        if (it != rreq_seen[node_id].end()) {
            std::cout << "Node " << node_id << " already processed this RREQ" << std::endl;
            return; // Already processed this RREQ
        }

        // Mark RREQ as seen
        rreq_seen[node_id][std::make_pair(rreq.source_id, rreq.rreq_id)] = simulationTime;

        // Update reverse route to source
        updateReverseRoute(node_id, rreq);

        // If this node is the destination
        if (node_id == rreq.destination_id) {
            std::cout << "Node " << node_id << " is the destination, generating RREP" << std::endl;
            // Generate RREP
            generateRREP(rreq, node_id);
        } else {
            // Check if this node has a fresh enough route to the destination
            auto route_it = route_tables[node_id].find(rreq.destination_id);
            if (route_it != route_tables[node_id].end() && route_it->second.valid &&
                route_it->second.sequence_number >= rreq.destination_sequence) {

                // If this is a direct path between base stations and we're trying to use an intermediate node
                // that has a direct connection, we should not generate an RREP
                if (is_base_to_base && route_it->second.hop_count <= 1) {
                    std::cout << "Node " << node_id << " has a direct route to destination, but this is a base-to-base path. Rebroadcasting RREQ." << std::endl;
                    broadcastRREQ(rreq, node_id);
                } else {
                    // Generate RREP on behalf of the destination
                    std::cout << "Node " << node_id << " has a valid route to destination, generating RREP" << std::endl;
                    generateRREP(rreq, node_id);
                }
            } else {
                // Rebroadcast RREQ
                std::cout << "Node " << node_id << " rebroadcasting RREQ" << std::endl;
                broadcastRREQ(rreq, node_id);
            }
        }
    }

    // Update reverse route to source
    void updateReverseRoute(int node_id, const AODVRouteRequest& rreq) {
        // Get current route if it exists
        auto& route_table = route_tables[node_id];
        auto it = route_table.find(rreq.source_id);

        // Update route if it doesn't exist or if the sequence number is higher
        if (it == route_table.end() || rreq.source_sequence > it->second.sequence_number) {
            // Find previous hop (the node that sent this RREQ)
            int prev_hop = -1;
            for (const auto& entry : rreq_seen) {
                if (entry.first != node_id &&
                    entry.second.find(std::make_pair(rreq.source_id, rreq.rreq_id)) != entry.second.end()) {
                    // Check if this node is a neighbor
                    auto neighbors_it = connectivityMap.find(node_id);
                    if (neighbors_it != connectivityMap.end() &&
                        std::find(neighbors_it->second.begin(), neighbors_it->second.end(), entry.first) != neighbors_it->second.end()) {
                        prev_hop = entry.first;
                        break;
                    }
                }
            }

            if (prev_hop != -1) {
                // Create or update reverse route
                route_table[rreq.source_id] = AODVRouteEntry(
                    rreq.source_id,         // destination
                    prev_hop,               // next hop (towards source)
                    rreq.hop_count,         // hop count
                    rreq.source_sequence,   // sequence number
                    simulationTime + AODV_ROUTE_LIFETIME  // lifetime
                );

                std::cout << "Node " << node_id << " updated reverse route to "
                          << rreq.source_id << " via " << prev_hop << std::endl;
            }
        }
    }

    // Generate RREP
    void generateRREP(const AODVRouteRequest& rreq, int node_id) {
        // Increment sequence number if this is the destination
        if (node_id == rreq.destination_id) {
            sequence_numbers[node_id]++;
        }

        // Get sequence number to use in RREP
        int seq_num;
        if (node_id == rreq.destination_id) {
            seq_num = sequence_numbers[node_id];
        } else {
            // Use the sequence number from the route table
            seq_num = route_tables[node_id][rreq.destination_id].sequence_number;
        }

        // Create RREP
        AODVRouteReply rrep(
            rreq.source_id,      // source
            rreq.destination_id, // destination
            seq_num,             // destination sequence number
            (node_id == rreq.destination_id) ? 0 : route_tables[node_id][rreq.destination_id].hop_count, // hop count
            AODV_ROUTE_LIFETIME, // lifetime
            simulationTime        // timestamp
        );

        // Find next hop towards source using reverse route
        int next_hop = route_tables[node_id][rreq.source_id].next_hop_id;

        // Send RREP towards source
        sendRREP(rrep, node_id, next_hop);

        std::cout << "Node " << node_id << " generated RREP for RREQ " << rreq.rreq_id
                  << " to source " << rreq.source_id << std::endl;
    }

    // Send RREP to next hop
    void sendRREP(const AODVRouteReply& rrep, int node_id, int next_hop) {
        // Check if next hop is a neighbor
        auto it = connectivityMap.find(node_id);
        if (it == connectivityMap.end() ||
            std::find(it->second.begin(), it->second.end(), next_hop) == it->second.end()) {
            std::cout << "Cannot send RREP from " << node_id << " to " << next_hop
                      << ": not a neighbor" << std::endl;
            return;
        }

        // Add to pending RREPs
        pending_route_replies.push_back(rrep);

        // Update metrics
        routing_metrics.total_rrep_sent++;

        std::cout << "Node " << node_id << " sending RREP to " << next_hop << std::endl;
    }

    // Process RREP at a node
    void processRREP(const AODVRouteReply& rrep, int node_id) {
        // Update forward route to destination
        updateForwardRoute(node_id, rrep);

        // If this node is the source, we're done
        if (node_id == rrep.source_id) {
            std::cout << "RREP reached source " << node_id << ", route established to "
                      << rrep.destination_id << std::endl;
            return;
        }

        // Forward RREP towards source
        auto it = route_tables[node_id].find(rrep.source_id);
        if (it != route_tables[node_id].end() && it->second.valid) {
            int next_hop = it->second.next_hop_id;
            sendRREP(rrep, node_id, next_hop);
        } else {
            std::cout << "Cannot forward RREP from " << node_id << ": no route to source "
                      << rrep.source_id << std::endl;
        }
    }

    // Update forward route to destination
    void updateForwardRoute(int node_id, const AODVRouteReply& rrep) {
        // Find previous hop (the node that sent this RREP)
        int prev_hop = -1;
        for (const auto& entry : connectivityMap) {
            if (entry.first != node_id &&
                std::find(entry.second.begin(), entry.second.end(), node_id) != entry.second.end()) {
                // Check if this node has the RREP
                bool has_rrep = false;
                for (const auto& pending_rrep : pending_route_replies) {
                    if (pending_rrep.source_id == rrep.source_id &&
                        pending_rrep.destination_id == rrep.destination_id &&
                        std::abs(pending_rrep.timestamp - rrep.timestamp) < 0.001) {
                        has_rrep = true;
                        break;
                    }
                }

                if (has_rrep) {
                    prev_hop = entry.first;
                    break;
                }
            }
        }

        if (prev_hop != -1) {
            // Create or update forward route
            auto& route_table = route_tables[node_id];
            route_table[rrep.destination_id] = AODVRouteEntry(
                rrep.destination_id,    // destination
                prev_hop,               // next hop (towards destination)
                rrep.hop_count + 1,     // hop count
                rrep.destination_sequence, // sequence number
                simulationTime + AODV_ROUTE_LIFETIME  // lifetime
            );

            std::cout << "Node " << node_id << " updated forward route to "
                      << rrep.destination_id << " via " << prev_hop << std::endl;
        }
    }

    // Generate RERR for a broken link
    void generateRERR(int node_id, int broken_link_id) {
        std::vector<int> affected_destinations;

        // Find all destinations that use the broken link as next hop
        auto& route_table = route_tables[node_id];
        for (const auto& entry : route_table) {
            if (entry.second.valid && entry.second.next_hop_id == broken_link_id) {
                affected_destinations.push_back(entry.first);

                // Invalidate the route
                route_table[entry.first].valid = false;
            }
        }

        // If no destinations are affected, do nothing
        if (affected_destinations.empty()) {
            return;
        }

        // Create and broadcast RERR for each affected destination
        for (int dest_id : affected_destinations) {
            AODVRouteError rerr(
                node_id,                        // source
                dest_id,                        // destination
                route_table[dest_id].sequence_number, // sequence number
                simulationTime                  // timestamp
            );

            broadcastRERR(rerr, node_id);

            // Update metrics
            routing_metrics.total_route_failures++;
        }
    }

    // Broadcast RERR to neighbors
    void broadcastRERR(const AODVRouteError& rerr, int node_id) {
        // Get neighbors of the node
        auto it = connectivityMap.find(node_id);
        if (it == connectivityMap.end()) {
            return; // No neighbors
        }

        // Add to pending RERRs
        pending_route_errors.push_back(rerr);

        // Update metrics
        routing_metrics.total_rerr_sent++;

        std::cout << "Node " << node_id << " broadcasting RERR for destination "
                  << rerr.destination_id << std::endl;
    }

    // Process RERR at a node
    void processRERR(const AODVRouteError& rerr, int node_id) {
        // Check if this node has a route to the destination via the source of the RERR
        auto& route_table = route_tables[node_id];
        auto it = route_table.find(rerr.destination_id);

        if (it != route_table.end() && it->second.valid) {
            // Check if the route uses the source of the RERR as next hop
            if (it->second.next_hop_id == rerr.source_id) {
                // Invalidate the route
                route_table[rerr.destination_id].valid = false;

                // Propagate RERR
                broadcastRERR(rerr, node_id);

                std::cout << "Node " << node_id << " invalidated route to "
                          << rerr.destination_id << " due to RERR" << std::endl;
            }
        }
    }

    // Process all pending AODV messages
    void processAODVMessages() {
        std::cout << "\n=== PROCESSING AODV MESSAGES ===\n";
        std::cout << "Pending RREQs: " << pending_route_requests.size() << std::endl;
        std::cout << "Pending RREPs: " << pending_route_replies.size() << std::endl;
        std::cout << "Pending RERRs: " << pending_route_errors.size() << std::endl;

        // Make copies of the pending messages to avoid modification during processing
        std::vector<AODVRouteRequest> rreqs = pending_route_requests;
        std::vector<AODVRouteReply> rreps = pending_route_replies;
        std::vector<AODVRouteError> rerrs = pending_route_errors;

        // Clear the pending messages before processing to avoid duplicates
        pending_route_requests.clear();
        pending_route_replies.clear();
        pending_route_errors.clear();

        // Process RREQs
        for (const auto& rreq : rreqs) {
            std::cout << "Processing RREQ from " << rreq.source_id << " to " << rreq.destination_id
                      << " (ID: " << rreq.rreq_id << ", hops: " << rreq.hop_count << ")" << std::endl;

            // Find all nodes that received this RREQ
            for (const auto& entry : rreq_seen) {
                int node_id = entry.first;
                if (entry.second.find(std::make_pair(rreq.source_id, rreq.rreq_id)) != entry.second.end()) {
                    processRREQ(rreq, node_id);
                }
            }
        }

        // Process RREPs
        for (const auto& rrep : rreps) {
            std::cout << "Processing RREP from " << rrep.destination_id << " to " << rrep.source_id
                      << " (hops: " << rrep.hop_count << ")" << std::endl;

            // Find all nodes that should process this RREP
            for (const auto& node_routes : route_tables) {
                int node_id = node_routes.first;
                auto it = node_routes.second.find(rrep.source_id);
                if (it != node_routes.second.end() && it->second.valid) {
                    processRREP(rrep, node_id);
                }
            }
        }

        // Process RERRs
        for (const auto& rerr : rerrs) {
            std::cout << "Processing RERR from " << rerr.source_id << " for destination " << rerr.destination_id << std::endl;

            // Find all nodes that should process this RERR
            for (const auto& entry : connectivityMap) {
                int node_id = entry.first;
                if (std::find(entry.second.begin(), entry.second.end(), rerr.source_id) != entry.second.end()) {
                    processRERR(rerr, node_id);
                }
            }
        }

        // Print routing tables after processing
        std::cout << "\n=== ROUTING TABLES AFTER PROCESSING ===\n";
        for (const auto& node_routes : route_tables) {
            int node_id = node_routes.first;
            std::cout << "Node " << node_id << " routing table:" << std::endl;

            for (const auto& route : node_routes.second) {
                std::cout << "  Destination: " << route.second.destination_id
                          << ", Next hop: " << route.second.next_hop_id
                          << ", Hops: " << route.second.hop_count
                          << ", Valid: " << (route.second.valid ? "YES" : "NO")
                          << ", Lifetime: " << route.second.lifetime
                          << std::endl;
            }
        }
        std::cout << "==============================\n";
    }

    // Hybrid A*-AODV routing function
    std::vector<int> findRouteHybrid(int source_id, int destination_id) {
        std::cout << "\n=== FINDING HYBRID A*-AODV ROUTE ===\n";
        std::cout << "Source: " << source_id << ", Destination: " << destination_id << std::endl;

        // Check if this is a path between base stations
        bool is_base_to_base = (source_id == transmitter_base_id && destination_id == receiver_base_id) ||
                              (source_id == receiver_base_id && destination_id == transmitter_base_id);

        if (is_base_to_base) {
            std::cout << "Path between base stations requires drone relays" << std::endl;
        }

        // First, check if a valid route exists in the AODV routing table
        std::vector<int> aodv_path;
        auto it = route_tables.find(source_id);
        if (it != route_tables.end()) {
            auto route_it = it->second.find(destination_id);
            if (route_it != it->second.end() && route_it->second.valid &&
                route_it->second.lifetime > simulationTime) {
                // Route exists and is valid
                int current = source_id;
                aodv_path.push_back(current);

                // Follow the route until we reach the destination
                while (current != destination_id) {
                    current = route_tables[current][destination_id].next_hop_id;
                    aodv_path.push_back(current);

                    // Prevent infinite loops
                    if (aodv_path.size() > AODV_NET_DIAMETER) {
                        std::cout << "Route too long, possible loop detected" << std::endl;
                        aodv_path.clear();
                        break;
                    }
                }

                // If this is a direct path between base stations (path length = 2), reject it
                if (is_base_to_base && aodv_path.size() <= 2) {
                    std::cout << "Rejecting direct path between base stations" << std::endl;
                    aodv_path.clear();
                }

                if (!aodv_path.empty()) {
                    std::cout << "Found valid AODV route in routing table: ";
                    for (size_t i = 0; i < aodv_path.size(); ++i) {
                        std::cout << aodv_path[i];
                        if (i < aodv_path.size() - 1) std::cout << " -> ";
                    }
                    std::cout << std::endl;
                }
            }
        }

        // Next, try to find a route using A*
        std::cout << "Running A* algorithm to find optimal path..." << std::endl;
        std::vector<int> astar_path = findRouteAStar(source_id, destination_id);

        if (!astar_path.empty()) {
            std::cout << "Found A* route: ";
            for (size_t i = 0; i < astar_path.size(); ++i) {
                std::cout << astar_path[i];
                if (i < astar_path.size() - 1) std::cout << " -> ";
            }
            std::cout << std::endl;
        } else {
            std::cout << "A* could not find a route" << std::endl;
        }

        // If we have both an AODV path and an A* path, compare them
        if (!aodv_path.empty() && !astar_path.empty()) {
            // Choose the shorter path
            if (astar_path.size() < aodv_path.size()) {
                std::cout << "Using A* path (shorter): " << astar_path.size() << " hops vs AODV: " << aodv_path.size() << " hops" << std::endl;

                // Update the AODV routing tables with the A* path
                updateRoutingTablesWithPath(source_id, destination_id, astar_path);

                return astar_path;
            } else {
                std::cout << "Using AODV path (shorter or equal): " << aodv_path.size() << " hops vs A*: " << astar_path.size() << " hops" << std::endl;
                return aodv_path;
            }
        }

        // If we only have an A* path, use it and update AODV tables
        if (!astar_path.empty()) {
            std::cout << "Using A* path (no valid AODV path found)" << std::endl;

            // Update the AODV routing tables with the A* path
            updateRoutingTablesWithPath(source_id, destination_id, astar_path);

            return astar_path;
        }

        // If we only have an AODV path, use it
        if (!aodv_path.empty()) {
            std::cout << "Using AODV path (no valid A* path found)" << std::endl;
            return aodv_path;
        }

        // If we don't have any path yet, try AODV route discovery
        std::cout << "No existing routes found, initiating AODV route discovery..." << std::endl;
        initiateRouteDiscovery(source_id, destination_id);
        processAODVMessages();

        // Check if AODV route discovery was successful
        it = route_tables.find(source_id);
        if (it != route_tables.end()) {
            auto route_it = it->second.find(destination_id);
            if (route_it != it->second.end() && route_it->second.valid) {
                // Route exists and is valid
                int current = source_id;
                std::vector<int> new_path;
                new_path.push_back(current);

                // Follow the route until we reach the destination
                while (current != destination_id) {
                    current = route_tables[current][destination_id].next_hop_id;
                    new_path.push_back(current);

                    // Prevent infinite loops
                    if (new_path.size() > AODV_NET_DIAMETER) {
                        std::cout << "Route too long, possible loop detected" << std::endl;
                        return std::vector<int>();
                    }
                }

                // If this is a direct path between base stations (path length = 2), reject it
                if (is_base_to_base && new_path.size() <= 2) {
                    std::cout << "Rejecting direct path between base stations after route discovery" << std::endl;
                    return std::vector<int>();
                }

                std::cout << "AODV route discovery successful, found path: ";
                for (size_t i = 0; i < new_path.size(); ++i) {
                    std::cout << new_path[i];
                    if (i < new_path.size() - 1) std::cout << " -> ";
                }
                std::cout << std::endl;

                return new_path;
            }
        }

        // If all else fails, try one more A* search (in case connectivity changed)
        if (astar_path.empty()) {
            std::cout << "Trying A* one more time..." << std::endl;
            astar_path = findRouteAStar(source_id, destination_id);

            if (!astar_path.empty()) {
                std::cout << "Found A* route on second attempt: ";
                for (size_t i = 0; i < astar_path.size(); ++i) {
                    std::cout << astar_path[i];
                    if (i < astar_path.size() - 1) std::cout << " -> ";
                }
                std::cout << std::endl;

                // Update the AODV routing tables with the A* path
                updateRoutingTablesWithPath(source_id, destination_id, astar_path);

                return astar_path;
            }
        }

        // Still no route, return empty path
        std::cout << "No route found using either A* or AODV" << std::endl;
        return std::vector<int>();
    }

    // Helper function to update AODV routing tables with a path found by A*
    void updateRoutingTablesWithPath(int source_id, int destination_id, const std::vector<int>& path) {
        if (path.size() < 2) return; // Path must have at least source and destination

        std::cout << "Updating AODV routing tables with A* path" << std::endl;

        // Count this as a route discovery
        routing_metrics.total_route_discoveries++;

        // Simulate RREQ/RREP messages that would have been sent in AODV
        // Each node in the path would send a RREQ
        routing_metrics.total_rreq_sent += path.size() - 1;

        // Each node in the reverse path would send a RREP
        routing_metrics.total_rrep_sent += path.size() - 1;

        std::cout << "  Simulated " << (path.size() - 1) << " RREQ and " << (path.size() - 1) << " RREP messages" << std::endl;

        // Update forward route (from source to destination)
        for (size_t i = 0; i < path.size() - 1; ++i) {
            int current_node = path[i];
            int next_hop = path[i + 1];

            // Create or update route entry
            route_tables[current_node][destination_id] = AODVRouteEntry(
                destination_id,         // destination
                next_hop,               // next hop
                path.size() - i - 1,    // hop count to destination
                sequence_numbers[destination_id], // destination sequence number
                simulationTime + AODV_ROUTE_LIFETIME  // lifetime
            );

            std::cout << "  Updated forward route: Node " << current_node
                      << " -> Destination " << destination_id
                      << " via " << next_hop << std::endl;
        }

        // Update reverse route (from destination to source)
        for (size_t i = path.size() - 1; i > 0; --i) {
            int current_node = path[i];
            int next_hop = path[i - 1];

            // Create or update route entry
            route_tables[current_node][source_id] = AODVRouteEntry(
                source_id,              // destination
                next_hop,               // next hop
                i,                      // hop count to source
                sequence_numbers[source_id], // source sequence number
                simulationTime + AODV_ROUTE_LIFETIME  // lifetime
            );

            std::cout << "  Updated reverse route: Node " << current_node
                      << " -> Source " << source_id
                      << " via " << next_hop << std::endl;
        }
    }

    // Find route using AODV (kept for compatibility)
    std::vector<int> findRouteAODV(int source_id, int destination_id) {
        // Now just calls the hybrid function
        return findRouteHybrid(source_id, destination_id);
    }

    // Create a new packet for routing
    void createPacket(int source_id, int destination_id) {
        // Find source and destination drones
        int source_idx = -1, dest_idx = -1;
        for (size_t i = 0; i < drones.size(); i++) {
            if (drones[i].id == source_id) source_idx = i;
            if (drones[i].id == destination_id) dest_idx = i;
        }

        if (source_idx == -1 || dest_idx == -1) {
            std::cerr << "Invalid source or destination ID" << std::endl;
            return;
        }

        // Print source and destination locations
        std::cout << "Creating packet from Drone " << source_id
                  << " at (" << drones[source_idx].x << ", " << drones[source_idx].y << ", " << drones[source_idx].z << ")"
                  << " to Drone " << destination_id
                  << " at (" << drones[dest_idx].x << ", " << drones[dest_idx].y << ", " << drones[dest_idx].z << ")"
                  << std::endl;

        active_packets.emplace_back(source_id, destination_id, simulationTime);
    }

    // Process all active packets
    void processPackets(double timeStep) {
        std::vector<RoutingPacket> completed_packets;

        for (auto& packet : active_packets) {
            if (packet.delivered) {
                completed_packets.push_back(packet);
                continue;
            }

            // Find current drone
            int drone_idx = -1;
            for (size_t i = 0; i < drones.size(); i++) {
                if (drones[i].id == packet.current_node_id) {
                    drone_idx = i;
                    break;
                }
            }

            if (drone_idx == -1) continue; // Current node not found

            // Check if drone is active or is a base station
            if (!drones[drone_idx].is_base_station &&
                drones[drone_idx].state != Drone::ACTIVE &&
                drones[drone_idx].state != Drone::ASCENDING) {
                continue; // Skip inactive drones that are not base stations
            }

            // Consume battery for packet processing
            double energy_wh = PACKET_PROCESSING_POWER * timeStep;
            double voltage = 11.1; // Typical drone battery voltage
            double energy_mah = energy_wh / voltage;
            double percentage_consumed = (energy_mah / drones[drone_idx].battery_capacity) * 100.0;
            drones[drone_idx].battery_level -= percentage_consumed;

            // If this is the destination, mark as delivered
            if (packet.current_node_id == packet.destination_id) {
                packet.delivered = true;
                double latency = simulationTime - packet.creation_time;
                routing_metrics.total_end_to_end_latency += latency;
                routing_metrics.total_packets_delivered++;

                // Check if this is a base station to base station packet
                std::string source_type = (packet.source_id == transmitter_base_id) ? "Transmitter Base Station" : "Drone";
                std::string dest_type = (packet.destination_id == receiver_base_id) ? "Receiver Base Station" : "Drone";

                std::cout << "Packet delivered from " << source_type << " " << packet.source_id
                          << " to " << dest_type << " " << packet.destination_id
                          << " with latency " << latency << "s" << std::endl;
                completed_packets.push_back(packet);
                continue;
            }

            // Find next hop using hybrid A*-AODV approach
            std::vector<int> route = findRouteHybrid(packet.current_node_id, packet.destination_id);

            // Debug connectivity information
            std::cout << "Connectivity for node " << packet.current_node_id << ": ";
            auto conn_it = connectivityMap.find(packet.current_node_id);
            if (conn_it != connectivityMap.end()) {
                for (auto neighbor : conn_it->second) {
                    std::cout << neighbor << " ";
                }
            } else {
                std::cout << "No connections";
            }
            std::cout << std::endl;

            // Print the hybrid path
            std::cout << "Hybrid A*-AODV path for packet from " << packet.source_id << " to " << packet.destination_id << ": ";
            if (route.empty()) {
                std::cout << "No path found" << std::endl;
                continue;
            } else {
                for (size_t i = 0; i < route.size(); ++i) {
                    std::cout << route[i];
                    if (i < route.size() - 1) {
                        std::cout << " -> ";
                    }
                }
                std::cout << std::endl;
            }

            if (route.size() < 2) {
                // No path found, try again next tick
                continue;
            }

            // Get next hop (second element in the route)
            int next_hop = route[1];

            // Check if next hop is in communication range
            auto it = connectivityMap.find(packet.current_node_id);
            if (it == connectivityMap.end() ||
                std::find(it->second.begin(), it->second.end(), next_hop) == it->second.end()) {
                // Next hop not in range, try again next tick
                continue;
            }

            // Calculate transmission power based on distance
            int next_hop_idx = -1;
            for (size_t i = 0; i < drones.size(); i++) {
                if (drones[i].id == next_hop) {
                    next_hop_idx = i;
                    break;
                }
            }

            if (next_hop_idx != -1) {
                double distance = drones[drone_idx].distanceTo(drones[next_hop_idx]);
                double tx_power = calculateTransmissionPower(distance);

                // Consume battery for transmission
                double tx_energy_mah = (tx_power * timeStep) / (11.1 * 3.6);
                double tx_percentage = (tx_energy_mah / drones[drone_idx].battery_capacity) * 100.0;
                drones[drone_idx].battery_level -= tx_percentage;

                // Forward packet to next hop
                packet.current_node_id = next_hop;
                packet.last_hop_time = simulationTime;
                packet.path.push_back(next_hop);

                std::cout << "Packet forwarded from Drone " << drones[drone_idx].id
                          << " to Drone " << next_hop
                          << " (distance: " << distance << "m, power: " << tx_power << "W)" << std::endl;
            }
        }

        // Remove delivered packets from active_packets
        for (const auto& packet : completed_packets) {
            active_packets.erase(
                std::remove_if(active_packets.begin(), active_packets.end(),
                    [&packet](const RoutingPacket& p) {
                        return p.source_id == packet.source_id &&
                               p.destination_id == packet.destination_id &&
                               p.creation_time == packet.creation_time;
                    }),
                active_packets.end());
        }
    }

    // Add method to print routing metrics
    void printRoutingMetrics() {
        std::cout << "=== Routing Metrics ===" << std::endl;
        std::cout << "Packets Delivered: " << routing_metrics.total_packets_delivered << std::endl;
        std::cout << "Average End-to-End Latency: " << routing_metrics.getAverageLatency() << "s" << std::endl;
        std::cout << "Average A* Execution Time: " << routing_metrics.getAverageAStarTime() << "s" << std::endl;
        std::cout << "Average AODV Route Discovery: " << routing_metrics.getAverageRouteDiscoveryTime() << " messages" << std::endl;
        std::cout << "Total End-to-End Latency (including path calculation): "
                  << routing_metrics.getAverageLatency() + routing_metrics.getAverageAStarTime() << "s" << std::endl;
        std::cout << "RREQ Sent: " << routing_metrics.total_rreq_sent << std::endl;
        std::cout << "RREP Sent: " << routing_metrics.total_rrep_sent << std::endl;
        std::cout << "RERR Sent: " << routing_metrics.total_rerr_sent << std::endl;
        std::cout << "Route Discoveries: " << routing_metrics.total_route_discoveries << std::endl;
        std::cout << "Route Failures: " << routing_metrics.total_route_failures << std::endl;
        std::cout << "======================" << std::endl;

        // Print current active packets
        if (!active_packets.empty()) {
            std::cout << "Active Packets: " << active_packets.size() << std::endl;
            for (const auto& packet : active_packets) {
                std::string source_type = (packet.source_id == transmitter_base_id) ? "Transmitter Base" : "Drone";
                std::string dest_type = (packet.destination_id == receiver_base_id) ? "Receiver Base" : "Drone";
                std::cout << "  Packet from " << source_type << " " << packet.source_id
                          << " to " << dest_type << " " << packet.destination_id
                          << ", current node: " << packet.current_node_id << std::endl;
            }
        }
    }



    // Calculate transmission power based on distance
    double calculateTransmissionPower(double distance) {
        // Base power for short distances
        double base_power = 0.1; // 0.1W for close drones

        // Additional power based on distance (quadratic increase)
        double distance_factor = 0.001 * distance * distance;

        // Total power in Watts
        return base_power + distance_factor;
    }

    // Calculate path loss based on distance and drone parameters
    double calculatePathLoss(double distance, const Drone& drone) {
        // Free space path loss formula: FSPL(dB) = 20*log10(d) + 20*log10(f) - 147.55
        double fspl = 20.0 * std::log10(distance) + 20.0 * std::log10(fc/1e6) - 147.55;

        // Add additional loss based on path loss exponent
        double additional_loss = 10.0 * drone.path_loss_exp * std::log10(distance);

        return fspl + additional_loss;
    }

    // Simplified path loss calculation when drone parameters aren't available
    double calculatePathLoss(double distance) {
        // Free space path loss formula with default path loss exponent of 2.0
        double fspl = 20.0 * std::log10(distance) + 20.0 * std::log10(fc/1e6) - 147.55;

        // Add additional loss based on default path loss exponent (2.0 for free space)
        double additional_loss = 10.0 * 2.0 * std::log10(distance);

        return fspl + additional_loss;
    }

    // Add this function to print the entire connectivity map
    void printConnectivityMap() {
        std::cout << "\n=== CONNECTIVITY MAP DETAILS ===\n";
        for (const auto& entry : connectivityMap) {
            std::cout << "Drone " << entry.first << " is connected to: ";
            if (entry.second.empty()) {
                std::cout << "NO DRONES";
            } else {
                for (int neighbor : entry.second) {
                    std::cout << neighbor << " ";
                }
            }
            std::cout << std::endl;
        }
        std::cout << "==============================\n";
    }
};


// --- Main Function ---
int main() {
    // --- Simulation Parameters ---
    const int NUM_DRONES = 20;           // Increased number of drones for better relay coverage
    const double WORLD_WIDTH = 100.0;   // Width of the simulation area
    const double WORLD_HEIGHT = 100.0;  // Height of the simulation area
    const double WORLD_MIN_HEIGHT = 20.0; // Minimum height for drones
    const double WORLD_MAX_HEIGHT = 100.0; // Maximum height for drones
    const double COMM_RANGE = 50.0;     // Increased communication range for better connectivity
    const int SIMULATION_STEPS = 1000;    // The number of times we will run the simulation
    const double TIME_STEP = 1.0;       // Duration of each time step

    // Create and run the simulator with 3D parameters
    DroneNetworkSimulator simulator(NUM_DRONES, WORLD_WIDTH, WORLD_HEIGHT, COMM_RANGE,
                                   WORLD_MIN_HEIGHT, WORLD_MAX_HEIGHT);
    simulator.runSimulation(SIMULATION_STEPS, TIME_STEP);

    return 0;
}
