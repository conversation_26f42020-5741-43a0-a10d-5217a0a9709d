#ifndef RR_ALGORITHM_H
#define RR_ALGORITHM_H

#include <vector>
#include <map>
#include <set>

// Forward declarations for RR algorithm functions
struct vertex;
struct heap;

// Interface for the simulator to provide connectivity information
class ConnectivityProvider {
public:
    virtual const std::map<int, std::set<int>>& getConnectivityMap() const = 0;
    virtual int getTransmitterBaseId() const = 0;
    virtual int getReceiverBaseId() const = 0;
    virtual double calculateDistance(int drone1_id, int drone2_id) const = 0;
    virtual ~ConnectivityProvider() {}
};

// Global pointer to the connectivity provider
extern ConnectivityProvider* g_connectivity_provider;

// Implementation of rr_compute_shortest_paths
void rr_compute_shortest_paths(vertex* g, heap* queue, int source_id, int* distances, int* predecessors, int num_nodes);

// Function to find a path using RR algorithm
std::vector<int> findPathRR(int source_id, int destination_id, int num_nodes, vertex* graph, heap* queue);

// Stub implementations for RR functions
extern "C" {
    vertex* g_create_graph(int num_nodes);
    void g_free_graph(vertex** g, int num_nodes);
    void g_insert_edge(vertex* g, int from_id, int to_id, int cost);
    heap* heap_new();
    void heap_insert(void* node, heap* queue);
    void* heap_extract(heap* queue);
    bool heap_is_empty(heap* queue);
    bool heap_is_added(void* node);
    void heap_update(void* node, int cost, heap* queue);
}

#endif // RR_ALGORITHM_H
