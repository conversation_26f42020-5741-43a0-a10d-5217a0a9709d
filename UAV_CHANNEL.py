import random
import statistics
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

import math

# Number of Relayed UAVs
N_Relayed_UAVs = 20

# Generate random coordinates of Relayed UAVs
X_UAVs = np.array([random.uniform(-50, 50) for i in range(N_Relayed_UAVs)])
Y_UAVs = np.array([random.uniform(-50, 50) for i in range(N_Relayed_UAVs)])
Z_UAVs = np.array([random.uniform(50, 100) for i in range(N_Relayed_UAVs)])

# Arrange the coordinates in x, y, z format
xyz = np.zeros([N_Relayed_UAVs, 3])
for i in range(N_Relayed_UAVs):
    xyz[i] = [X_UAVs[i], Y_UAVs[i], Z_UAVs[i]]
    
# Display the Randomly distributed Relayed UAVs in a 3D space
fig = plt.figure(1)
ax = fig.add_subplot(111, projection='3d')
ax.xaxis.pane.fill = False
ax.yaxis.pane.fill = False
ax.zaxis.pane.fill = False
ax.xaxis.pane.set_edgecolor('none')
ax.yaxis.pane.set_edgecolor('none')
ax.zaxis.pane.set_edgecolor('none')
ax.scatter(X_UAVs, Y_UAVs, Z_UAVs)
ax.set_zlim([0, np.max(Z_UAVs + 5)])
ax.box = False
ax.grid(False)
ax.set_xlabel('X')
ax.set_ylabel('Y')
ax.set_zlabel('Z')



"""
######################
Calculate the elevation angle between the transmitter
and the Relayed-enabled UAVs
######################
"""
d_H = np.zeros(N_Relayed_UAVs)
d_V = np.zeros(N_Relayed_UAVs)
theta = np.zeros(N_Relayed_UAVs)
for i in range(N_Relayed_UAVs):
    d_H[i] = math.sqrt(((xyz[i][0])**2 + (xyz[i][1])**2))
    d_V[i] = xyz[i][2]
    theta[i] = math.atan(d_V[i]/d_H[i])

# Plot the histogram of the theta
hist, bins = np.histogram(theta*180/np.pi, bins = 10)
plt.figure(2)
plt.hist(theta*180/np.pi, bins=20, alpha=0.5, color='b')


"""
#####################
Calculate the LOS probability
#####################
"""
Prob_LOS = np.zeros(N_Relayed_UAVs)
a1 = 12.08
b1 = 0.11
for i in range(N_Relayed_UAVs):
    Prob_LOS[i] = 1/(1 + a1*math.exp((-b1*(theta[i] - a1))))


"""
#####################
Calculate the Pathloss exponent
#####################
"""
Path_Loss_Expo = np.zeros(N_Relayed_UAVs)
alpha_pi_by_2 = 2
alpha_zero = 3.5
Prob_LOS_pi_by_2 = 1/(1 + a1*math.exp((-b1*(np.pi/2 - a1))))
Prob_LOS_zero = 1/(1 + a1*math.exp((-b1*(0 - a1))))
a2 = (alpha_pi_by_2 - alpha_zero)/(Prob_LOS_pi_by_2 - Prob_LOS_zero)
b2 = alpha_zero - a2*Prob_LOS_zero

for i in range(N_Relayed_UAVs):
    Path_Loss_Expo[i] = a2*Prob_LOS[i] + b2
    

"""
######################
Chanel Realization
######################
"""

Number_Channel_Realization = 10**3

################################################################
# Define the Doppler paramters
# Carrier frequency in Hz
fc = 2.4e9
# Relative speed between the relayed UAV and the transmitter
v = 20
# speed of light in m/s
c = 3e8
# Symbol duration in seconds
# This may also be interpreted as a frame duration
Ts = 1e-6
# Evaluate the Doppler shift factor
f_Doppler = v*fc/c
################################################################

K = np.zeros(N_Relayed_UAVs)
K_zero = 1
K_pi_by_2 = 15
a3 = K_zero
b3 = (2/np.pi)*math.log(K_pi_by_2/K_zero)

P_LOS_plus_NLOS = 1

sigma = np.zeros(N_Relayed_UAVs)
s = np.zeros([N_Relayed_UAVs])
h_real = np.zeros([N_Relayed_UAVs, Number_Channel_Realization])
h_imag = np.zeros([N_Relayed_UAVs, Number_Channel_Realization])
h = np.zeros([N_Relayed_UAVs, Number_Channel_Realization])\
    + (np.zeros([N_Relayed_UAVs, Number_Channel_Realization]))*1j
h_abs = np.zeros([N_Relayed_UAVs, Number_Channel_Realization])
h_mean = np.zeros(N_Relayed_UAVs)

h_Doppler = np.zeros([N_Relayed_UAVs, Number_Channel_Realization])\
        + (np.zeros([N_Relayed_UAVs, Number_Channel_Realization]))*1j


for i in range(N_Relayed_UAVs):
    K[i] = a3*math.exp(b3*theta[i])
    sigma[i] = P_LOS_plus_NLOS/math.sqrt(2*(K[i] + 1))
    s[i] = math.sqrt(K[i]/(K[i] + 1)*P_LOS_plus_NLOS)
    h_real[i] = (sigma[i]*(np.array([np.random.normal(0, 1) for i in range(Number_Channel_Realization)])) \
           + s[i])
    h_imag[i] = (sigma[i]*(np.array([np.random.normal(0, 1) for i in range(Number_Channel_Realization)])) \
           + 0)
    h[i] = h_real[i] + h_imag[i]*1j

    """
    Introduce Impact of Doppler Shift
    """
    h_Doppler[i] = h[i]*np.exp(-1j*2*np.pi*f_Doppler*Ts)
    h_abs[i] = abs(h_Doppler[i])
    h_mean[i] = sum(h_abs[i])/len(h_abs[i])
    h_abs[i] = abs(h[i])
    h_mean[i] = sum(h_abs[i])/len(h_abs[i])

hist, bins = np.histogram(h_abs[0]/max(h_abs[0]), bins = 100)
plt.figure(3)
plt.hist(h_abs[0]/max(h_abs[0]), bins=100, alpha=0.5, color='b')
plt.show()


"""
#######################
Calculate the Pathloss
#######################
"""

Pt = 1
d = np.zeros(N_Relayed_UAVs)
Received_Signal_Power = np.zeros(N_Relayed_UAVs)
for i in range(N_Relayed_UAVs):
    d[i] = math.sqrt(d_H[i]**2 + d_V[i]**2)
    Received_Signal_Power[i] = h_mean[i]*((d[i])**(-Path_Loss_Expo[i]))*Pt
