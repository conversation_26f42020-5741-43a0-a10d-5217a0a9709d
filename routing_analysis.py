import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import re
import os

# Create output directory if it doesn't exist
output_dir = 'routing_analysis_plots/hybrid'
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# Load the data
df = pd.read_csv('routing_metrics_hybrid.csv')

# Extract relay nodes from paths
def extract_relay_nodes(path):
    if isinstance(path, str) and '->' in path:
        nodes = path.strip('"').split('->')
        if len(nodes) > 2:  # If there's at least one relay
            return nodes[1:-1]  # Return all nodes except source and destination
    return []

# Apply the function to create a new column with relay nodes
df['RelayNodes'] = df['Path'].apply(extract_relay_nodes)

# Extract routing algorithm used for each path
def extract_algorithm(path_info):
    if isinstance(path_info, str) and '[' in path_info and ']' in path_info:
        match = re.search(r'\[(.*?)\]', path_info)
        if match:
            return match.group(1)
    return "Unknown"

# Apply the function to create a new column with algorithm info
df['Algorithm'] = df['Path'].apply(extract_algorithm)

# 1. Packets Delivered Over Time - Individual high-res plot
plt.figure(figsize=(10, 6))
plt.plot(df['Time'], df['PacketsDelivered'], linewidth=2)
plt.title('Packets Delivered Over Time (Hybrid)', fontsize=14)
plt.xlabel('Simulation Time', fontsize=12)
plt.ylabel('Packets Delivered', fontsize=12)
plt.grid(True)
plt.tight_layout()
plt.savefig(f'{output_dir}/packets_delivered.png', dpi=300)
plt.close()

# 2. Average Latency Over Time - Individual high-res plot
plt.figure(figsize=(10, 6))
plt.plot(df['Time'], df['AverageLatency'], linewidth=2, color='green')
plt.title('Average Latency Over Time (Hybrid)', fontsize=14)
plt.xlabel('Simulation Time', fontsize=12)
plt.ylabel('Average Latency (s)', fontsize=12)
plt.grid(True)
plt.tight_layout()
plt.savefig(f'{output_dir}/average_latency.png', dpi=300)
plt.close()

# 3. Algorithm Execution Time - Individual high-res plot
plt.figure(figsize=(10, 6))
plt.plot(df['Time'], df['AverageAStarTime'], linewidth=2, color='red')  # Fixed column name
plt.title('Average Routing Execution Time (Hybrid)', fontsize=14)
plt.xlabel('Simulation Time', fontsize=12)
plt.ylabel('Execution Time (s)', fontsize=12)
plt.grid(True)
plt.tight_layout()
plt.savefig(f'{output_dir}/routing_execution_time.png', dpi=300)
plt.close()

# 4. Relay Node Frequency - Individual high-res plot
relay_counts = {}
for relays in df['RelayNodes']:
    for node in relays:
        if node in relay_counts:
            relay_counts[node] += 1
        else:
            relay_counts[node] = 1

# Sort by frequency
sorted_relays = sorted(relay_counts.items(), key=lambda x: x[1], reverse=True)

if sorted_relays:
    plt.figure(figsize=(12, 6))
    nodes, counts = zip(*sorted_relays[:20])  # Show top 20 nodes
    plt.bar(nodes, counts, color='purple')
    plt.title('Relay Node Frequency (Hybrid)', fontsize=14)
    plt.xlabel('Node ID', fontsize=12)
    plt.ylabel('Frequency', fontsize=12)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/relay_node_frequency.png', dpi=300)
    plt.close()

# 5. Algorithm Distribution - New plot for hybrid approach
if 'Algorithm' in df.columns:
    algo_counts = df['Algorithm'].value_counts()
    plt.figure(figsize=(10, 6))
    algo_counts.plot(kind='bar', color='teal')
    plt.title('Routing Algorithm Distribution', fontsize=14)
    plt.xlabel('Algorithm', fontsize=12)
    plt.ylabel('Frequency', fontsize=12)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/algorithm_distribution.png', dpi=300)
    plt.close()
    
    # 5.1 Algorithm Performance Comparison
    algo_latency = df.groupby('Algorithm')['AverageLatency'].mean()
    plt.figure(figsize=(10, 6))
    algo_latency.plot(kind='bar', color='orange')
    plt.title('Average Latency by Algorithm', fontsize=14)
    plt.xlabel('Algorithm', fontsize=12)
    plt.ylabel('Average Latency (s)', fontsize=12)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/algorithm_latency.png', dpi=300)
    plt.close()

# 6. Combined dashboard
plt.figure(figsize=(15, 10))
plt.subplot(2, 3, 1)
plt.plot(df['Time'], df['PacketsDelivered'])
plt.title('Packets Delivered Over Time')
plt.xlabel('Simulation Time')
plt.ylabel('Packets Delivered')
plt.grid(True)

plt.subplot(2, 3, 2)
plt.plot(df['Time'], df['AverageLatency'])
plt.title('Average Latency Over Time')
plt.xlabel('Simulation Time')
plt.ylabel('Average Latency (s)')
plt.grid(True)

plt.subplot(2, 3, 3)
plt.plot(df['Time'], df['AverageAStarTime'])  # Fixed column name
plt.title('Average Routing Execution Time')
plt.xlabel('Simulation Time')
plt.ylabel('Execution Time (s)')
plt.grid(True)

plt.subplot(2, 3, 4)
if sorted_relays:
    nodes, counts = zip(*sorted_relays[:10])  # Top 10 for dashboard
    plt.bar(nodes, counts)
    plt.title('Top Relay Nodes')
    plt.xlabel('Node ID')
    plt.ylabel('Frequency')
    plt.xticks(rotation=45)

plt.subplot(2, 3, 5)
if 'Algorithm' in df.columns:
    algo_counts.plot(kind='bar')
    plt.title('Algorithm Distribution')
    plt.xlabel('Algorithm')
    plt.ylabel('Count')
    plt.xticks(rotation=45)

plt.tight_layout()
plt.savefig(f'{output_dir}/hybrid_routing_analysis.png', dpi=300)
plt.show()

# Print summary statistics
print("=== Hybrid Routing Metrics Analysis ===")
print(f"Total packets delivered: {df['PacketsDelivered'].max()}")
print(f"Final average latency: {df['AverageLatency'].iloc[-1]:.4f} seconds")
print(f"Average routing execution time: {df['AverageAStarTime'].mean():.6f} seconds")  # Fixed column name

# Calculate packet delivery rate
simulation_duration = df['Time'].max() - df['Time'].min()
delivery_rate = df['PacketsDelivered'].max() / simulation_duration
print(f"Packet delivery rate: {delivery_rate:.4f} packets/second")

# Algorithm statistics
if 'Algorithm' in df.columns:
    print("\nAlgorithm usage statistics:")
    for algo, count in algo_counts.items():
        print(f"  {algo}: {count} uses ({count/len(df)*100:.1f}%)")
    
    print("\nAlgorithm performance:")
    for algo, latency in algo_latency.items():
        print(f"  {algo}: {latency:.4f} seconds average latency")

# Analyze most common paths
paths = df['Path'].dropna().tolist()
path_counts = {}
for path in paths:
    if path in path_counts:
        path_counts[path] += 1
    else:
        path_counts[path] = 1

print("\nMost common routing paths:")
for path, count in sorted(path_counts.items(), key=lambda x: x[1], reverse=True)[:5]:
    print(f"  {path}: {count} occurrences")

# Path frequency visualization
top_paths = sorted(path_counts.items(), key=lambda x: x[1], reverse=True)[:10]
if top_paths:
    plt.figure(figsize=(12, 6))
    paths, counts = zip(*top_paths)
    plt.bar(paths, counts, color='orange')
    plt.title('Most Common Routing Paths (Hybrid)', fontsize=14)
    plt.xlabel('Path', fontsize=12)
    plt.ylabel('Frequency', fontsize=12)
    plt.xticks(rotation=45, ha='right')
    plt.tight_layout()
    plt.savefig(f'{output_dir}/common_paths.png', dpi=300)
    plt.close()

# Calculate path hop statistics
hop_counts = []
for path in paths:
    if isinstance(path, str):
        # Extract just the path part if there's algorithm info
        path_only = path.split('[')[0].strip() if '[' in path else path
        nodes = path_only.strip('"').split('->')
        hop_counts.append(len(nodes) - 1)

if hop_counts:
    print(f"\nAverage hop count: {np.mean(hop_counts):.2f}")
    print(f"Min hop count: {np.min(hop_counts)}")
    print(f"Max hop count: {np.max(hop_counts)}")
    
    # Hop count distribution
    plt.figure(figsize=(10, 6))
    plt.hist(hop_counts, bins=range(min(hop_counts), max(hop_counts)+2), alpha=0.7, color='blue')
    plt.title('Distribution of Hop Counts (Hybrid)', fontsize=14)
    plt.xlabel('Number of Hops', fontsize=12)
    plt.ylabel('Frequency', fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/hop_count_distribution.png', dpi=300)
    plt.close()

# Hop count by algorithm
if 'Algorithm' in df.columns:
    # Create a new DataFrame with algorithm and hop count
    hop_data = []
    for i, row in df.iterrows():
        if isinstance(row['Path'], str):
            path_only = row['Path'].split('[')[0].strip() if '[' in row['Path'] else row['Path']
            nodes = path_only.strip('"').split('->')
            hop_data.append({
                'Algorithm': row['Algorithm'],
                'HopCount': len(nodes) - 1
            })
    
    hop_df = pd.DataFrame(hop_data)
    algo_hops = hop_df.groupby('Algorithm')['HopCount'].mean()
    
    plt.figure(figsize=(10, 6))
    algo_hops.plot(kind='bar', color='green')
    plt.title('Average Hop Count by Algorithm', fontsize=14)
    plt.xlabel('Algorithm', fontsize=12)
    plt.ylabel('Average Hop Count', fontsize=12)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/algorithm_hop_count.png', dpi=300)
    plt.close()
